// ==UserScript==
// @name         OpenFrontIO Smart Auto-Pilot v6.0 - True Full Bot
// @namespace    https://openfront.io/
// @version      6.0.0
// @description  Complete autonomous bot using game's internal API
// @match        https://openfront.io/*
// @match        https://*.openfront.io/*
// @match        http://localhost/*
// @match        http://127.0.0.1/*
// @grant        none
// @run-at       document-idle
// ==/UserScript==

(function () {
  "use strict";

  console.log("╔════════════════════════════════════════════════════════════╗");
  console.log("║     🤖 Smart Auto-Pilot v6.0 - True Full Bot             ║");
  console.log("╚════════════════════════════════════════════════════════════╝");

  /* ===== CONFIGURATION ===== */
  const CONFIG = {
    enabled: false,
    mode: 'full-auto', // 'full-auto' or 'assisted'

    // Bot parameters (adjusted for better early game)
    triggerRatio: 0.50,        // Attack when 50% full (was 70%, too high for early game)
    reserveRatioNeutral: 0.15, // Keep 15% vs neutral
    reserveRatioPlayers: 0.25, // Keep 25% vs players
    attackInterval: 5000,      // Attack every 5 seconds

    // Assisted mode
    optimalRatio: 0.42,        // Maintain 42% for optimal growth
  };

  let lastAttackTime = 0;
  let gameAPI = null;

  /* ===== EVENT CLASSES ===== */
  // We need to create proper event class instances for the EventBus
  class SendAttackIntentEvent {
    constructor(targetID, troops) {
      this.targetID = targetID;
      this.troops = troops;
    }
  }

  /* ===== GAME API ACCESS ===== */
  function getGameAPI() {
    if (gameAPI && gameAPI.isValid()) {
      return gameAPI;
    }

    const controlPanel = document.querySelector('control-panel');
    if (!controlPanel) {
      console.log("[API] Control panel not found");
      return null;
    }

    const game = controlPanel.game;
    const eventBus = controlPanel.eventBus;
    const uiState = controlPanel.uiState;

    if (!game || !eventBus || !uiState) {
      console.log("[API] Game components not ready:", { game: !!game, eventBus: !!eventBus, uiState: !!uiState });
      return null;
    }

    const myPlayer = game.myPlayer();
    if (!myPlayer) {
      console.log("[API] Player not spawned yet");
      return null;
    }

    gameAPI = {
      game,
      eventBus,
      uiState,
      myPlayer,
      isValid: () => {
        const player = game.myPlayer();
        return player !== null && player.isAlive();
      }
    };

    console.log("[API] ✅ Game API initialized!");
    console.log("[API] Player:", myPlayer.name?.() || myPlayer.displayName?.() || "Unknown");

    // Debug: Log available methods
    console.log("[API] 🔍 Player methods:", Object.getOwnPropertyNames(Object.getPrototypeOf(myPlayer)).filter(m => typeof myPlayer[m] === 'function').slice(0, 20));
    console.log("[API] 🔍 EventBus type:", eventBus.constructor.name);

    return gameAPI;
  }

  /* ===== ATTACK LOGIC ===== */
  async function selectAttackTarget(api) {
    const { game, myPlayer } = api;

    try {
      // Get terra nullius (neutral land) - smallID 0
      const terraNullius = game.playerBySmallID(0);

      // Get my border tiles to find neighbors
      const borderData = await myPlayer.borderTiles();
      const borderTiles = borderData.borderTiles || [];

      console.log(`[Target] Checking ${borderTiles.length} border tiles`);

      // Find all neighboring owners
      const neighborOwners = new Map(); // Map of smallID -> owner

      for (const borderTile of borderTiles) {
        const neighbors = game.neighbors(borderTile);
        for (const neighborTile of neighbors) {
          if (!game.isLand(neighborTile)) continue;

          const ownerID = game.ownerID(neighborTile);
          const mySmallID = myPlayer.smallID();

          if (ownerID !== mySmallID && !neighborOwners.has(ownerID)) {
            const owner = game.playerBySmallID(ownerID);
            neighborOwners.set(ownerID, owner);
          }
        }
      }

      console.log(`[Target] Found ${neighborOwners.size} unique neighbors`);

      // 1. Prefer terra nullius (neutral land)
      if (neighborOwners.has(0)) {
        console.log("[Target] 🎯 Selected: Terra Nullius (neutral land)");
        return {
          target: terraNullius,
          reserveRatio: CONFIG.reserveRatioNeutral,
          targetName: "Terra Nullius"
        };
      }

      // 2. Filter to enemies (not allies or teammates)
      const enemies = [];
      for (const [smallID, owner] of neighborOwners) {
        if (!owner.isPlayer || !owner.isPlayer()) continue;
        if (myPlayer.isFriendly && myPlayer.isFriendly(owner)) continue;
        enemies.push(owner);
      }

      if (enemies.length === 0) {
        console.log("[Target] No valid enemies found");
        return null;
      }

      // 3. Select weakest enemy (lowest troop density)
      let weakestEnemy = null;
      let lowestDensity = Infinity;

      for (const enemy of enemies) {
        const troops = enemy.troops();
        const tiles = enemy.numTilesOwned();
        const density = troops / tiles;

        if (density < lowestDensity) {
          lowestDensity = density;
          weakestEnemy = enemy;
        }
      }

      if (weakestEnemy) {
        const name = weakestEnemy.name?.() || weakestEnemy.displayName?.() || "Unknown";
        console.log(`[Target] 🎯 Selected: ${name} (density: ${lowestDensity.toFixed(1)})`);
        return {
          target: weakestEnemy,
          reserveRatio: CONFIG.reserveRatioPlayers,
          targetName: name
        };
      }

    } catch (error) {
      console.error("[Target] Error selecting target:", error);
      console.error(error.stack);
    }

    return null;
  }

  async function executeAttack(api) {
    const { game, eventBus, myPlayer, uiState } = api;

    try {
      // 1. Check troop levels
      const troops = myPlayer.troops();
      const maxTroops = game.config().maxTroops(myPlayer);
      const ratio = troops / maxTroops;

      console.log(`[Attack] Troops: ${troops}/${maxTroops} (${(ratio * 100).toFixed(1)}%)`);

      if (ratio < CONFIG.triggerRatio) {
        console.log(`[Attack] ⏳ Waiting for troops (${(ratio * 100).toFixed(1)}% < ${(CONFIG.triggerRatio * 100).toFixed(0)}%)`);
        return false;
      }

      // 2. Select target (async)
      const targetInfo = await selectAttackTarget(api);
      if (!targetInfo) {
        console.log("[Attack] ❌ No valid targets");
        return false;
      }

      const { target, reserveRatio, targetName } = targetInfo;

      // 3. Calculate attack troops
      const reserveTroops = maxTroops * reserveRatio;
      const attackTroops = Math.max(0, troops - reserveTroops);

      if (attackTroops < 1) {
        console.log("[Attack] ❌ Not enough troops after reserve");
        return false;
      }

      // 4. Set attack ratio in UI
      const attackPct = Math.round((attackTroops / maxTroops) * 100);
      uiState.attackRatio = attackPct / 100;

      // Update slider
      const slider = document.querySelector('#attack-ratio');
      if (slider) {
        slider.value = attackPct;
        slider.dispatchEvent(new Event('input', { bubbles: true }));
      }

      // 5. Send attack event
      console.log("╔════════════════════════════════════════════════════════════╗");
      console.log(`║  ⚔️  ATTACKING: ${targetName.padEnd(43)} ║`);
      console.log("╚════════════════════════════════════════════════════════════╝");
      console.log(`[Attack] 📊 Troops: ${attackTroops} (${attackPct}%)`);
      console.log(`[Attack] 🛡️  Reserve: ${(reserveRatio * 100).toFixed(0)}%`);

      // Create attack event using proper class
      const targetID = target.id ? target.id() : null;
      const attackEvent = new SendAttackIntentEvent(targetID, attackTroops);

      console.log(`[Attack] 🎯 Target ID: ${targetID}`);
      console.log(`[Attack] 📤 Emitting attack event...`);

      // Emit the event
      eventBus.emit(attackEvent);

      console.log("[Attack] ✅ Attack command sent!");
      return true;

    } catch (error) {
      console.error("[Attack] ❌ Error executing attack:", error);
      console.error(error.stack);
      return false;
    }
  }

  /* ===== ASSISTED MODE ===== */
  function manageAttackRatio(api) {
    const { game, myPlayer, uiState } = api;

    try {
      const troops = myPlayer.troops();
      const maxTroops = game.config().maxTroops(myPlayer);
      const ratio = troops / maxTroops;

      let targetAttackPct = 0;
      let reason = "";

      if (ratio > CONFIG.optimalRatio + 0.05) {
        const excess = troops - (CONFIG.optimalRatio * maxTroops);
        targetAttackPct = Math.min(80, (excess / maxTroops) * 100);
        reason = `EXCESS (${(ratio * 100).toFixed(1)}% > ${(CONFIG.optimalRatio * 100).toFixed(0)}%)`;
      } else if (ratio < CONFIG.optimalRatio - 0.05) {
        targetAttackPct = 0;
        reason = `GROWING (${(ratio * 100).toFixed(1)}% < ${(CONFIG.optimalRatio * 100).toFixed(0)}%)`;
      } else {
        reason = `OPTIMAL (${(ratio * 100).toFixed(1)}% ≈ ${(CONFIG.optimalRatio * 100).toFixed(0)}%)`;
      }

      const currentPct = Math.round(uiState.attackRatio * 100);
      const targetPct = Math.round(targetAttackPct);

      if (Math.abs(currentPct - targetPct) > 2) {
        console.log(`[Assisted] Adjusting: ${currentPct}% → ${targetPct}% (${reason})`);

        uiState.attackRatio = targetPct / 100;

        const slider = document.querySelector('#attack-ratio');
        if (slider) {
          slider.value = targetPct;
          slider.dispatchEvent(new Event('input', { bubbles: true }));
        }
      }

    } catch (error) {
      console.error("[Assisted] Error:", error);
    }
  }

  /* ===== MAIN LOOP ===== */
  async function mainLoop() {
    if (!CONFIG.enabled) return;

    const api = getGameAPI();
    if (!api) return;

    const now = performance.now();

    if (CONFIG.mode === 'full-auto') {
      // Full auto-pilot: attack periodically
      if (now - lastAttackTime >= CONFIG.attackInterval) {
        const attacked = await executeAttack(api);
        if (attacked) {
          lastAttackTime = now;
        }
      }
    } else {
      // Assisted mode: manage attack ratio
      manageAttackRatio(api);
    }
  }

  /* ===== UI ===== */
  function createUI() {
    const container = document.createElement('div');
    container.id = 'autopilot-v6-ui';
    container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 999999;
      background: linear-gradient(135deg, rgba(15,23,42,0.95), rgba(30,41,59,0.95));
      color: #f1f5f9;
      border: 1px solid rgba(148,163,184,0.3);
      border-radius: 12px;
      padding: 16px;
      font-family: system-ui, -apple-system, sans-serif;
      font-size: 13px;
      min-width: 280px;
      box-shadow: 0 20px 25px -5px rgba(0,0,0,0.4);
    `;

    container.innerHTML = `
      <div style="font-size: 14px; font-weight: 700; color: #10b981; margin-bottom: 12px;">
        🤖 Auto-Pilot v6.0
      </div>
      <div style="margin-bottom: 12px;">
        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
          <input type="checkbox" id="enable-autopilot" ${CONFIG.enabled ? 'checked' : ''}>
          <span>Enable Auto-Pilot</span>
        </label>
      </div>
      <div style="margin-bottom: 12px;">
        <label style="display: block; margin-bottom: 4px; font-size: 11px; color: #cbd5e1;">Mode:</label>
        <select id="autopilot-mode" style="width: 100%; padding: 6px; border-radius: 4px; background: rgba(0,0,0,0.3); color: white; border: 1px solid rgba(148,163,184,0.3);">
          <option value="full-auto" ${CONFIG.mode === 'full-auto' ? 'selected' : ''}>🤖 Full Auto-Pilot</option>
          <option value="assisted" ${CONFIG.mode === 'assisted' ? 'selected' : ''}>🎯 Assisted Mode</option>
        </select>
      </div>
      <div id="autopilot-status" style="font-size: 11px; color: #94a3b8; padding: 8px; background: rgba(0,0,0,0.2); border-radius: 4px;">
        Status: Disabled
      </div>
    `;

    document.body.appendChild(container);

    // Event listeners
    document.getElementById('enable-autopilot').addEventListener('change', (e) => {
      CONFIG.enabled = e.target.checked;
      updateStatus();
      console.log(`[UI] Auto-pilot ${CONFIG.enabled ? 'ENABLED' : 'DISABLED'}`);
    });

    document.getElementById('autopilot-mode').addEventListener('change', (e) => {
      CONFIG.mode = e.target.value;
      updateStatus();
      console.log(`[UI] Mode changed to: ${CONFIG.mode}`);
    });

    updateStatus();
  }

  function updateStatus() {
    const statusEl = document.getElementById('autopilot-status');
    if (!statusEl) return;

    const modeText = CONFIG.mode === 'full-auto' ? '🤖 Full Auto' : '🎯 Assisted';
    const statusText = CONFIG.enabled ? '🟢 ACTIVE' : '⭕ DISABLED';

    statusEl.innerHTML = `
      <div><strong>Status:</strong> ${statusText}</div>
      <div><strong>Mode:</strong> ${modeText}</div>
    `;
  }

  /* ===== INITIALIZATION ===== */
  function init() {
    console.log("[Init] Waiting for game to load...");

    // Wait for control panel to be available
    const checkInterval = setInterval(() => {
      const controlPanel = document.querySelector('control-panel');
      if (controlPanel) {
        console.log("[Init] ✅ Control panel found!");
        clearInterval(checkInterval);
        createUI();

        // Start main loop
        setInterval(mainLoop, 1000);
        console.log("[Init] ✅ Main loop started!");
      }
    }, 1000);
  }

  // Start when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

})();
