<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Early Game Attack Fix - Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e293b, #334155);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            color: white;
            min-height: 100vh;
        }
        
        .fix-info {
            background: rgba(34,197,94,0.1);
            border: 1px solid rgba(34,197,94,0.3);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            max-width: 900px;
        }
        
        .fix-info h1 {
            color: #22c55e;
            margin-top: 0;
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before, .after {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            border-left: 4px solid #ef4444;
        }
        
        .after {
            border-left: 4px solid #22c55e;
        }
        
        .highlight {
            background: rgba(245,158,11,0.2);
            padding: 2px 6px;
            border-radius: 4px;
            color: #f59e0b;
            font-weight: 600;
        }
        
        .test-controls {
            background: rgba(59,130,246,0.1);
            border: 1px solid rgba(59,130,246,0.3);
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .test-button {
            background: rgba(59,130,246,0.2);
            border: 1px solid rgba(59,130,246,0.4);
            color: #60a5fa;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: rgba(59,130,246,0.3);
        }
        
        .status {
            font-family: monospace;
            background: rgba(0,0,0,0.5);
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="fix-info">
        <h1>🚀 Early Game Attack Fix - No More 1% Trap!</h1>
        
        <p>Fixed the critical issue where the bot would keep you at 1% attack ratio during the crucial early expansion phase.</p>
        
        <div class="before-after">
            <div class="before">
                <h3>❌ Before (Broken)</h3>
                <ul>
                    <li>Bot used 38% optimal ratio even in early game</li>
                    <li>25% critical threshold prevented early attacks</li>
                    <li>3% tolerance was too tight</li>
                    <li>15% minimum attack ratio too high</li>
                    <li><strong>Result: Stuck at 1% for first 5 minutes!</strong></li>
                </ul>
            </div>
            
            <div class="after">
                <h3>✅ After (Fixed)</h3>
                <ul>
                    <li>Early game uses 25% optimal ratio for expansion</li>
                    <li>5% critical threshold allows early attacks</li>
                    <li>10% tolerance for more attacking</li>
                    <li>5% minimum attack ratio for small attacks</li>
                    <li><strong>Result: Aggressive early expansion!</strong></li>
                </ul>
            </div>
        </div>
        
        <h3>🎯 Key Changes:</h3>
        <ul>
            <li><span class="highlight">Early Game Detection</span> - First 5 minutes use different logic</li>
            <li><span class="highlight">Aggressive Expansion</span> - Even "optimal" range attacks at 15%+ in early game</li>
            <li><span class="highlight">Lower Thresholds</span> - Much more lenient requirements for attacking</li>
            <li><span class="highlight">Smart Scaling</span> - Gradually transitions to normal logic after 5 minutes</li>
        </ul>
        
        <h3>📊 Early Game Strategy:</h3>
        <ul>
            <li><strong>0-2 minutes:</strong> Claim Terra Nullius aggressively</li>
            <li><strong>2-5 minutes:</strong> Attack weak neighbors for territory</li>
            <li><strong>5+ minutes:</strong> Switch to optimal growth strategy</li>
        </ul>
    </div>
    
    <div class="test-controls">
        <h3>🧪 Test the Fix</h3>
        <p>Enable the bot and watch the console logs. You should see:</p>
        <ul>
            <li>"[Early Game] Using aggressive expansion ratios"</li>
            <li>"🚀 EARLY EXPANSION" or "🎯 EARLY OPTIMAL" status messages</li>
            <li>Attack ratios of 10-25% instead of 1%</li>
        </ul>
        
        <button class="test-button" onclick="simulateEarlyGame()">Simulate Early Game</button>
        <button class="test-button" onclick="simulateLateGame()">Simulate Late Game</button>
        <button class="test-button" onclick="resetGameTime()">Reset Game Timer</button>
        
        <div id="test-status" class="status">
            Ready to test. Enable the bot and check console logs.
        </div>
    </div>

    <!-- Mock game elements -->
    <input type="range" id="attack-ratio" min="1" max="100" value="1" style="display: none;">

    <!-- Load the userscript -->
    <script src="bot-help/new-player-bot-assistant.user.js"></script>
    
    <script>
        function updateStatus(message) {
            document.getElementById('test-status').textContent = message;
        }
        
        function simulateEarlyGame() {
            // Reset game start time to now (early game)
            if (window.STATE) {
                window.STATE.gameStartTime = Date.now();
                updateStatus('✅ Simulated early game start. Bot should use aggressive expansion logic.');
                console.log('🧪 Test: Simulated early game start');
            } else {
                updateStatus('❌ Bot not loaded yet');
            }
        }
        
        function simulateLateGame() {
            // Set game start time to 10 minutes ago (late game)
            if (window.STATE) {
                window.STATE.gameStartTime = Date.now() - (10 * 60 * 1000);
                updateStatus('✅ Simulated late game. Bot should use normal growth logic.');
                console.log('🧪 Test: Simulated late game (10 minutes in)');
            } else {
                updateStatus('❌ Bot not loaded yet');
            }
        }
        
        function resetGameTime() {
            if (window.STATE) {
                window.STATE.gameStartTime = null;
                updateStatus('✅ Reset game timer. Next calculation will set new start time.');
                console.log('🧪 Test: Reset game timer');
            } else {
                updateStatus('❌ Bot not loaded yet');
            }
        }
        
        // Mock game API
        window.mockGameAPI = {
            game: {
                config: () => ({ 
                    maxTroops: () => 80000, // Early game max troops
                }),
                myPlayer: () => ({
                    troops: () => 15000, // 18.75% ratio - should trigger early expansion
                    numTilesOwned: () => 5,
                    isAlive: () => true,
                    smallID: () => 1,
                    borderTiles: () => Promise.resolve({ borderTiles: [] }),
                    incomingAttacks: () => []
                })
            }
        };
        
        // Expose STATE for testing
        setTimeout(() => {
            if (typeof STATE !== 'undefined') {
                window.STATE = STATE;
                updateStatus('✅ Bot loaded. Ready for testing.');
            }
        }, 1000);
        
        console.log('🧪 Early Game Fix Test Page Loaded');
        console.log('📊 Mock player: 15k/80k troops (18.75% ratio)');
        console.log('🎯 Expected: Early game should attack at 15%+, late game should wait');
    </script>
</body>
</html>
