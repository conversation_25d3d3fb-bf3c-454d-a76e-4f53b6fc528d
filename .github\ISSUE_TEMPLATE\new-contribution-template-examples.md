---
name: New Contribution Template
about: "For new Contributions to be added to the Project Management Process "
title: ""
labels: ""
assignees: ""
---

By the time the contribution is ready for final review, the main post of the Issue should be able to serve as the PR description, supplemented by the testing results/peer reviews.

## Purpose

Very brief statement as to what this contribution's purpose is. Examples:

- Add new feature that allows users to create custom flags.
- Fix bug: modals not closing on game start.
- Add new Iceland map.

---

## Objectives

Please provide a short list of objectives for the contribution, the number and level of specificity of the items will depend on the scope of the contribution. Is the means by which completion can be measured and success of the changes assessed. May be the starting point for the creation of sub-issues if collaboration is needed/desired. Examples:

**For the Custom Flags Feature**

- Create UI for Flag Customization.
- Create customization features that provide a wide range of possibilities for users to express themselves.
- Ensure some customization features are limited to users with certain traits.
- Assess user traits related to limited customization against Discord roles.
- Create method for customized flags to be displayed correctly on all connected clients.
- Create means of restricting certain customization combinations to prevent hate symbols, or ensure it is not possible to create hate symbols with provided customizations.

**For a Bug Fix**

- Ensure all open modals except game start (and any other expected) modal close as expected when the game starts.
- Ensure all modals still function as expected prior to game start.

**For New Map**

- Add New Map, Iceland to the game for use in Singleplayer and Custom lobbies.
- Add map to lobby rotation.

---

## Description

Please provide a more detailed description of what is being changed/added, how it is being changed/added, provide any additional detail related to the objectives which would be needed or useful in assessing the contribution overall.

Examples of what info would be expected:

**For the Custom Flags Feature**

- Details about where the UI would be accessed from, mockups of a concept UI if they exist.
- What kind of customizations will be available.
- What traits will be provided limited customization features? (Patreons, Devs, Owner, etc.)
- What kind of customizations will be limited.
- Any high-level technical implementation details that may be useful in assessing viability, or which will require assistance from other contributors or the project owner (communication between the clients, use or access to a backend DB, discord integration, etc.)

**For a Bug Fix**

- Details about the bug's behavior
- Details about the cause of the bug (once known)
- Details about the fix
- Any secondary effects of implementing the fix

**For a New Map**

- Size of the map
- Number of Nation Bots
- Images of the Map
- Any interesting features of the map

---

[Discord Name if Different]
