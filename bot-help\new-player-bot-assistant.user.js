// ==UserScript==
// @name         OpenFrontIO - New Player Bot Assistant
// @namespace    https://openfront.io/
// @version      2.0.0
// @description  Comprehensive bot assistant for new players with smart autopilot, threat detection, target analysis, and learning features
// @match        https://openfront.io/*
// @match        https://*.openfront.io/*
// @match        http://localhost/*
// @match        http://127.0.0.1/*
// @grant        none
// @run-at       document-idle
// <AUTHOR> Community
// ==/UserScript==

(function () {
  "use strict";

  console.log("╔════════════════════════════════════════════════════════════╗");
  console.log("║                                                            ║");
  console.log("║     🤖 New Player Bot Assistant v2.0                       ║");
  console.log("║     Advanced AI-powered gameplay assistance                ║");
  console.log("║                                                            ║");
  console.log("╚════════════════════════════════════════════════════════════╝");

  /* ===== CONFIGURATION ===== */
  const CONFIG = {
    // Bot control
    enabled: false,
    botMode: 'balanced', // 'aggressive', 'balanced', 'defensive', 'manual'

    // Growth optimization
    baseOptimalRatio: 0.42,
    tolerance: 0.05, // Increased from 0.03 to allow more attacking
    aggressiveThreshold: 0.15,

    // Risk management
    minAttackRatio: 0.10, // Reduced from 0.15 to allow smaller attacks
    underAttackReserve: 0.30,
    criticalHealthThreshold: 0.15, // Reduced from 0.25 to allow earlier expansion

    // Early game settings (first 5 minutes)
    earlyGameDuration: 300000, // 5 minutes in milliseconds
    earlyGameMinAttack: 0.05, // 5% minimum for early expansion
    earlyGameOptimalRatio: 0.25, // Much lower optimal ratio for early game
    earlyGameTolerance: 0.10, // Much wider tolerance for early game

    // Threat detection
    threatUpdateInterval: 2000,
    threatTroopThreshold: 5000,
    borderPressureThreshold: 1.5,

    // Attack strategies
    smartAttackEnabled: true,
    opportunisticAttackEnabled: true,
    weakTargetRatio: 0.10,
    normalTargetRatio: 0.20,
    strongTargetRatio: 0.50,

    // Mode presets
    modes: {
      aggressive: {
        baseOptimalRatio: 0.45,
        minAttackRatio: 0.20,
        underAttackReserve: 0.20,
        weakTargetRatio: 0.15,
        normalTargetRatio: 0.30,
        strongTargetRatio: 0.60
      },
      balanced: {
        baseOptimalRatio: 0.42,
        minAttackRatio: 0.15,
        underAttackReserve: 0.30,
        weakTargetRatio: 0.10,
        normalTargetRatio: 0.20,
        strongTargetRatio: 0.50
      },
      defensive: {
        baseOptimalRatio: 0.38,
        minAttackRatio: 0.10,
        underAttackReserve: 0.40,
        weakTargetRatio: 0.05,
        normalTargetRatio: 0.15,
        strongTargetRatio: 0.40
      }
    },

    // Performance tracking
    trackPerformance: true,
    statsUpdateInterval: 10000,

    // UI settings
    showTutorial: true,
    showDetailedMetrics: true,
    showRecommendations: true,
    collapsed: false,
    position: { x: 20, y: 20 },
    widgetSize: { width: 420, height: 400 }, // Fixed initial height instead of 'auto'

    // Card states (collapsed/expanded)
    cardStates: {
      botControls: true,
      sliderTest: true,
      currentStatus: true,
      recommendations: true,
      targetAnalysis: true,
      diplomacy: true, // New diplomacy card
      performance: true
    }
  };

  // Load saved config
  const savedConfig = localStorage.getItem('newPlayerBot_config');
  if (savedConfig) {
    try {
      Object.assign(CONFIG, JSON.parse(savedConfig));
    } catch (e) {
      console.warn("[Config] Failed to load saved config:", e);
    }
  }

  function saveConfig() {
    localStorage.setItem('newPlayerBot_config', JSON.stringify(CONFIG));
  }

  /* ===== STATE TRACKING ===== */
  const STATE = {
    // Game state
    gameAPI: null,
    isFirstRun: true,
    lastUpdate: 0,
    lastThreatUpdate: 0,
    lastStatsUpdate: 0,
    gameStartTime: null, // Track when game started for early game detection

    // Neighbor tracking
    neighbors: new Map(),
    threats: [],
    opportunities: [],

    // Performance stats
    stats: {
      startTime: 0,
      startTroops: 0,
      startTerritories: 0,
      currentTroops: 0,
      currentTerritories: 0,
      attacksMade: 0,
      attacksReceived: 0,
      territoriesGained: 0,
      territoriesLost: 0,
      peakTroops: 0,
      totalGrowth: 0
    },

    // Strategic state
    currentOptimalRatio: 0.42,
    currentTarget: null,
    recommendedAction: 'WAIT',
    recommendedReason: 'Initializing...',

    // Border analysis
    borderData: new Map(),
    expansionTargets: [],

    // Attack history
    recentAttacks: [],
    recentDefenses: []
  };

  /* ===== GAME API ACCESS ===== */
  function getGameAPI() {
    if (STATE.gameAPI && STATE.gameAPI.isValid()) {
      return STATE.gameAPI;
    }

    const controlPanel = document.querySelector('control-panel');
    if (!controlPanel) return null;

    const game = controlPanel.game;
    const uiState = controlPanel.uiState;
    const eventBus = controlPanel.eventBus;

    if (!game || !uiState) return null;

    const myPlayer = game.myPlayer();
    if (!myPlayer || !myPlayer.isAlive()) return null;

    STATE.gameAPI = {
      game,
      uiState,
      eventBus,
      myPlayer,
      isValid: () => {
        const player = game.myPlayer();
        return player !== null && player.isAlive();
      }
    };

    // Initialize stats if first time
    if (STATE.stats.startTime === 0) {
      STATE.stats.startTime = Date.now();
      STATE.stats.startTroops = myPlayer.troops();
      STATE.stats.startTerritories = myPlayer.numTilesOwned();
      STATE.stats.currentTroops = myPlayer.troops();
      STATE.stats.currentTerritories = myPlayer.numTilesOwned();
    }

    console.log("[API] ✅ Game API initialized!");
    console.log("[API] Player:", myPlayer.name?.() || myPlayer.displayName?.() || "Unknown");

    return STATE.gameAPI;
  }

  /* ===== SLIDER CONTROL ===== */
  function setSliderValue(percentage) {
    const slider = document.querySelector('#attack-ratio');
    if (!slider) return false;

    try {
      // CRITICAL: Game enforces minimum 1% (slider min="1"), so 0% becomes 1%
      // We need to handle this properly to avoid the 0%/1% confusion
      let safePercentage = percentage;

      // If we want 0%, we need to respect that the game minimum is 1%
      if (safePercentage === 0) {
        // Set to 1% (game minimum) but log that we intended 0%
        safePercentage = 1;
        console.log(`[Slider] Requested 0% but game minimum is 1% - setting to 1%`);
      } else if (safePercentage > 0 && safePercentage < CONFIG.minAttackRatio * 100) {
        // Enforce our bot's minimum attack ratio for actual attacks
        console.warn(`[SAFETY] Blocked ${safePercentage.toFixed(1)}% attack - enforcing minimum ${CONFIG.minAttackRatio * 100}%`);
        safePercentage = CONFIG.minAttackRatio * 100;
      }

      const max = Number(slider.max || 100);
      const min = Number(slider.min || 1); // Game slider min is 1
      const targetValue = Math.round(Math.max(min, Math.min(max, safePercentage)));

      // Only update if changed
      if (Math.abs(Number(slider.value) - targetValue) < 1) {
        return true;
      }

      requestAnimationFrame(() => {
        slider.value = String(targetValue);
        slider.dispatchEvent(new Event('input', { bubbles: true }));
        slider.dispatchEvent(new Event('change', { bubbles: true }));

        // Update visual
        const fillTrack = slider.closest('.relative')?.querySelector('.bg-red-500\\/60');
        if (fillTrack) {
          fillTrack.style.width = `${(targetValue / max) * 100}%`;
        }
      });

      console.log(`[Slider] Set to ${targetValue}% (requested: ${percentage}%)`);
      return true;
    } catch (error) {
      console.error("[Slider] Error setting value:", error);
      return false;
    }
  }

  /* ===== MODE MANAGEMENT ===== */
  function applyMode(mode) {
    if (CONFIG.modes[mode]) {
      Object.assign(CONFIG, CONFIG.modes[mode]);
      console.log(`[Mode] Applied ${mode} mode settings`);
    }
  }

  /* ===== EARLY GAME DETECTION ===== */
  function isEarlyGame() {
    if (!STATE.gameStartTime) {
      STATE.gameStartTime = Date.now();
    }
    return (Date.now() - STATE.gameStartTime) < CONFIG.earlyGameDuration;
  }

  /* ===== DIPLOMACY ANALYSIS ===== */
  async function analyzeDiplomacy(api) {
    const { game, myPlayer } = api;

    try {
      const analysis = {
        alliances: [],
        threats: [],
        opportunities: [],
        recommendations: []
      };

      // Analyze current alliances
      const allies = myPlayer.allies?.() || [];
      for (const ally of allies) {
        const allyTroops = ally.troops?.() || 0;
        const allyTiles = ally.numTilesOwned?.() || 0;
        const myTroops = myPlayer.troops();
        const myTiles = myPlayer.numTilesOwned();

        const powerRatio = allyTroops / Math.max(myTroops, 1);
        const sizeRatio = allyTiles / Math.max(myTiles, 1);

        let status = 'Balanced';
        if (powerRatio > 3) status = 'Much Stronger';
        else if (powerRatio > 1.5) status = 'Stronger';
        else if (powerRatio < 0.3) status = 'Much Weaker';
        else if (powerRatio < 0.7) status = 'Weaker';

        analysis.alliances.push({
          name: ally.name?.() || ally.displayName?.() || 'Unknown',
          troops: allyTroops,
          tiles: allyTiles,
          powerRatio: powerRatio,
          status: status,
          trustLevel: ally.type?.() === 'FAKEHUMAN' ? 'Nation (Reliable)' : 'Player (Variable)'
        });
      }

      // Find neighbors using the same method as target analysis (this works!)
      const borderData = await myPlayer.borderTiles();
      const borderTiles = borderData.borderTiles || [];

      const neighborOwners = new Map();
      for (const borderTile of borderTiles) {
        const neighbors = game.neighbors(borderTile);
        for (const neighborTile of neighbors) {
          if (!game.isLand(neighborTile)) continue;
          const ownerID = game.ownerID(neighborTile);
          const mySmallID = myPlayer.smallID();
          if (ownerID !== mySmallID && ownerID !== 0 && !neighborOwners.has(ownerID)) {
            const owner = game.playerBySmallID(ownerID);
            if (owner && owner.isPlayer && owner.isPlayer()) {
              neighborOwners.set(ownerID, owner);
            }
          }
        }
      }

      const neighbors = Array.from(neighborOwners.values());
      console.log(`[Diplomacy] Found ${neighbors.length} neighbors using border scan method`);

      for (const neighbor of neighbors) {
        if (!neighbor.isPlayer?.()) continue;

        const neighborTroops = neighbor.troops?.() || 0;
        const neighborTiles = neighbor.numTilesOwned?.() || 0;
        const isAllied = myPlayer.isAlliedWith?.(neighbor) || false;
        const isNation = neighbor.type?.() === 'FAKEHUMAN';
        const neighborName = neighbor.name?.() || neighbor.displayName?.() || `${isNation ? 'Nation' : 'Player'} ${neighbor.id?.() || 'Unknown'}`;

        // Get relation if available
        let relation = 'Unknown';
        let isHostile = false;
        try {
          if (myPlayer.relation && typeof myPlayer.relation === 'function') {
            const relationValue = myPlayer.relation(neighbor);
            // Relation enum: Hostile = -1, Neutral = 0, Friendly = 1
            if (relationValue < 0) {
              relation = 'Hostile';
              isHostile = true;
            } else if (relationValue > 0) {
              relation = 'Friendly';
            } else {
              relation = 'Neutral';
            }
          }
        } catch (e) {
          console.log(`[Diplomacy] Could not get relation with ${neighborName}`);
        }

        console.log(`[Diplomacy] Neighbor: ${neighborName}, Troops: ${neighborTroops}, Allied: ${isAllied}, Relation: ${relation}`);

        if (!isAllied) {
          const threatRatio = neighborTroops / Math.max(myPlayer.troops(), 1);

          // Threat detection: hostile relations OR significantly stronger
          if (isHostile || threatRatio > 1.3) {
            let threatLevel = 'Medium';
            if (isHostile && threatRatio > 1.5) threatLevel = 'High';
            else if (threatRatio > 2.5) threatLevel = 'High';

            analysis.threats.push({
              name: neighborName,
              troops: neighborTroops,
              threatLevel: threatLevel,
              type: isNation ? 'Nation' : 'Player',
              relation: relation,
              recommendation: threatLevel === 'High' ? 'Consider alliance or defense' : 'Monitor closely'
            });
          }
          // Opportunity detection: significantly weaker OR neutral/friendly weak neighbors
          else if (threatRatio < 0.9) {
            analysis.opportunities.push({
              name: neighborName,
              troops: neighborTroops,
              weakness: threatRatio < 0.5 ? 'Very Weak' : 'Weak',
              weaknessRatio: (1 / threatRatio).toFixed(1) + 'x',
              type: isNation ? 'Nation' : 'Player',
              relation: relation,
              recommendation: threatRatio < 0.5 ? 'Easy target' : 'Potential expansion'
            });
          }
        }
      }

      // Generate strategic recommendations
      console.log(`[Diplomacy] Analysis complete: ${analysis.alliances.length} allies, ${analysis.threats.length} threats, ${analysis.opportunities.length} opportunities`);

      if (analysis.alliances.length === 0) {
        analysis.recommendations.push('🤝 Consider forming alliances for protection');
      }
      if (analysis.threats.length > analysis.alliances.length) {
        analysis.recommendations.push('⚠️ More threats than allies - seek diplomatic protection');
      }
      if (analysis.opportunities.length > 0) {
        analysis.recommendations.push('🎯 Weak neighbors detected - expansion opportunity');
      }
      if (neighbors.length === 0) {
        analysis.recommendations.push('🔍 No neighbors detected - check game state');
      }

      return analysis;
    } catch (error) {
      console.warn('[Diplomacy] Analysis failed:', error);
      return { alliances: [], threats: [], opportunities: [], recommendations: [] };
    }
  }

  /* ===== DYNAMIC OPTIMAL RATIO CALCULATION ===== */
  function calculateOptimalRatio(maxTroops) {
    // Early game: much more aggressive expansion
    if (isEarlyGame()) {
      console.log("[Early Game] Using aggressive expansion ratios");
      return CONFIG.earlyGameOptimalRatio; // 25% for rapid expansion
    }

    // Based on game formula: growth(r) = [10 + (M×r)^0.73/4] × (1-r)
    if (maxTroops < 100000) return 0.38;
    if (maxTroops < 300000) return 0.40;
    if (maxTroops < 600000) return 0.42;
    if (maxTroops < 1000000) return 0.43;
    return 0.44;
  }

  /* ===== THREAT DETECTION ===== */
  function isUnderAttack(api) {
    try {
      const { myPlayer } = api;
      const incomingAttacks = myPlayer.incomingAttacks?.() || [];

      // Update defense history
      for (const attack of incomingAttacks) {
        const existing = STATE.recentDefenses.find(d => d.id === attack.id());
        if (!existing) {
          STATE.recentDefenses.push({
            id: attack.id(),
            attacker: attack.attacker().name?.() || "Unknown",
            troops: attack.troops(),
            timestamp: Date.now()
          });
          STATE.stats.attacksReceived++;
        }
      }

      // Clean old defenses (older than 30 seconds)
      const now = Date.now();
      STATE.recentDefenses = STATE.recentDefenses.filter(d => now - d.timestamp < 30000);

      return incomingAttacks.length > 0;
    } catch (error) {
      return false;
    }
  }

  /* ===== NEIGHBOR SCANNING ===== */
  async function scanNeighbors(api) {
    const { game, myPlayer } = api;

    try {
      const borderData = await myPlayer.borderTiles();
      const borderTiles = borderData.borderTiles || [];

      const neighborOwners = new Map();

      for (const borderTile of borderTiles) {
        const neighbors = game.neighbors(borderTile);
        for (const neighborTile of neighbors) {
          if (!game.isLand(neighborTile)) continue;

          const ownerID = game.ownerID(neighborTile);
          const mySmallID = myPlayer.smallID();

          if (ownerID !== mySmallID && !neighborOwners.has(ownerID)) {
            const owner = game.playerBySmallID(ownerID);
            neighborOwners.set(ownerID, owner);
          }
        }
      }

      const now = Date.now();

      // Update neighbor tracking
      for (const [ownerID, owner] of neighborOwners) {
        if (ownerID === 0) continue; // Skip terra nullius for tracking
        if (!owner.isPlayer || !owner.isPlayer()) continue;
        if (myPlayer.isFriendly && myPlayer.isFriendly(owner)) continue;

        const neighborID = owner.id();
        const troops = owner.troops();
        const tiles = owner.numTilesOwned();
        const density = troops / tiles;

        let neighborData = STATE.neighbors.get(neighborID);
        if (!neighborData) {
          neighborData = {
            player: owner,
            troops: troops,
            tiles: tiles,
            density: density,
            lastUpdate: now,
            troopHistory: [{ time: now, troops: troops }]
          };
          STATE.neighbors.set(neighborID, neighborData);
        } else {
          neighborData.troops = troops;
          neighborData.tiles = tiles;
          neighborData.density = density;
          neighborData.lastUpdate = now;
          neighborData.troopHistory.push({ time: now, troops: troops });

          if (neighborData.troopHistory.length > 20) {
            neighborData.troopHistory = neighborData.troopHistory.slice(-20);
          }
        }
      }

      // Clean up neighbors we no longer border
      const currentNeighborIDs = new Set(Array.from(neighborOwners.values())
        .filter(o => o.isPlayer && o.isPlayer())
        .map(o => o.id()));

      for (const neighborID of STATE.neighbors.keys()) {
        if (!currentNeighborIDs.has(neighborID)) {
          STATE.neighbors.delete(neighborID);
        }
      }
    } catch (error) {
      console.error("[Neighbors] Error scanning:", error);
    }
  }

  /* ===== THREAT ANALYSIS ===== */
  function analyzeThreat(neighborData, myTroops) {
    const { player, troops, density, troopHistory } = neighborData;

    let threatLevel = 'LOW';
    const reasons = [];

    // Check troop buildup
    if (troopHistory.length >= 2) {
      const recent = troopHistory[troopHistory.length - 1];
      const previous = troopHistory[Math.max(0, troopHistory.length - 5)];
      const troopGain = recent.troops - previous.troops;
      const timeSpan = (recent.time - previous.time) / 1000;

      if (troopGain > CONFIG.threatTroopThreshold && timeSpan > 0) {
        threatLevel = 'HIGH';
        reasons.push(`Rapid buildup: +${troopGain.toFixed(0)} troops`);
      }
    }

    // Check troop ratio
    const troopRatio = troops / myTroops;
    if (troopRatio > 1.5) {
      threatLevel = 'HIGH';
      reasons.push(`${(troopRatio * 100).toFixed(0)}% of your strength`);
    } else if (troopRatio > 1.0) {
      if (threatLevel === 'LOW') threatLevel = 'MEDIUM';
      reasons.push(`Equal strength`);
    }

    return {
      neighbor: player,
      threatLevel,
      reasons,
      troops,
      density,
      troopRatio
    };
  }

  function detectThreats(api) {
    const { myPlayer } = api;
    const myTroops = myPlayer.troops();

    STATE.threats = [];

    for (const neighborData of STATE.neighbors.values()) {
      const threat = analyzeThreat(neighborData, myTroops);

      if (threat.threatLevel !== 'LOW') {
        STATE.threats.push(threat);
      }
    }

    // Sort by threat level
    const threatOrder = { 'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
    STATE.threats.sort((a, b) => threatOrder[b.threatLevel] - threatOrder[a.threatLevel]);
  }

  /* ===== TARGET FINDING ===== */
  async function findAllTargets(api) {
    const { game, myPlayer } = api;

    try {
      const targets = [];
      const myTroops = myPlayer.troops();
      const myTiles = myPlayer.numTilesOwned();
      const myDensity = myTroops / myTiles;

      const borderData = await myPlayer.borderTiles();
      const borderTiles = borderData.borderTiles || [];

      const neighborOwners = new Map();
      for (const borderTile of borderTiles) {
        const neighbors = game.neighbors(borderTile);
        for (const neighborTile of neighbors) {
          if (!game.isLand(neighborTile)) continue;
          const ownerID = game.ownerID(neighborTile);
          const mySmallID = myPlayer.smallID();
          if (ownerID !== mySmallID && !neighborOwners.has(ownerID)) {
            const owner = game.playerBySmallID(ownerID);
            neighborOwners.set(ownerID, owner);
          }
        }
      }

      // Add Terra Nullius if available
      if (neighborOwners.has(0)) {
        targets.push({
          targetName: "Terra Nullius (Neutral Land)",
          winChance: 1.0,
          score: 1000,
          isNeutral: true,
          recommendedRatio: CONFIG.weakTargetRatio,
          threat: 'NONE',
          momentum: '✓'
        });
      }

      // Analyze enemy neighbors
      for (const [, owner] of neighborOwners) {
        if (!owner.isPlayer || !owner.isPlayer()) continue;
        if (myPlayer.isFriendly && myPlayer.isFriendly(owner)) continue;

        const enemyTroops = owner.troops();
        const enemyTiles = owner.numTilesOwned();
        const enemyDensity = enemyTroops / enemyTiles;

        // Calculate win chance
        const troopRatio = myTroops / (enemyTroops + 1);
        const densityRatio = myDensity / (enemyDensity + 0.1);
        const winChance = Math.min(0.95, (troopRatio + densityRatio) / 2);

        // Score
        const score = winChance * (1 / (enemyDensity + 1));

        // Get recommended ratio
        let recommendedRatio = CONFIG.normalTargetRatio;
        if (CONFIG.smartAttackEnabled) {
          if (winChance > 0.80) recommendedRatio = CONFIG.weakTargetRatio;
          else if (winChance > 0.60) recommendedRatio = CONFIG.normalTargetRatio;
          else recommendedRatio = CONFIG.strongTargetRatio;
        }

        // Get threat level
        const threat = STATE.threats.find(t => t.neighbor.id() === owner.id());
        const threatLevel = threat ? threat.threatLevel : 'LOW';

        targets.push({
          target: owner,
          targetName: owner.name?.() || owner.displayName?.() || "Unknown",
          density: enemyDensity,
          winChance: winChance,
          score: score,
          isNeutral: false,
          recommendedRatio: recommendedRatio,
          threat: threatLevel,
          momentum: winChance > 0.7 ? '↗' : winChance > 0.4 ? '→' : '↘'
        });
      }

      // Sort by score
      targets.sort((a, b) => b.score - a.score);

      return targets;

    } catch (error) {
      console.error("[Tactical] Error finding targets:", error);
      return [];
    }
  }

  /* ===== OPTIMAL SLIDER CALCULATION ===== */
  function calculateOptimalSlider(api) {
    const { game, myPlayer } = api;

    try {
      const troops = myPlayer.troops();
      const maxTroops = game.config().maxTroops(myPlayer);
      const currentRatio = troops / maxTroops;

      // Calculate dynamic optimal ratio
      const optimalRatio = calculateOptimalRatio(maxTroops);
      STATE.currentOptimalRatio = optimalRatio;

      // Check if under attack
      const underAttack = isUnderAttack(api);

      // Use early game settings if applicable
      const isEarly = isEarlyGame();
      const tolerance = isEarly ? CONFIG.earlyGameTolerance : CONFIG.tolerance;
      const minAttack = isEarly ? CONFIG.earlyGameMinAttack : CONFIG.minAttackRatio;
      const criticalThreshold = isEarly ? 0.05 : CONFIG.criticalHealthThreshold; // Very low threshold in early game

      let targetSlider = 0;
      let reason = "";
      let status = "OPTIMAL";
      let riskLevel = "LOW";

      // Critical health check (much more lenient in early game)
      if (currentRatio < criticalThreshold) {
        targetSlider = 0;
        reason = `🚨 CRITICAL - Below ${(CONFIG.criticalHealthThreshold * 100).toFixed(0)}% health`;
        status = "CRITICAL";
        riskLevel = "CRITICAL";
        STATE.recommendedAction = 'DEFEND';
        STATE.recommendedReason = 'Focus on defense and growth';
      }
      // Under attack
      else if (underAttack) {
        const safeRatio = currentRatio - CONFIG.underAttackReserve;
        if (safeRatio > 0) {
          targetSlider = Math.max(CONFIG.minAttackRatio * 100, (safeRatio / currentRatio) * 100);
          reason = `⚠️ UNDER ATTACK - Reserved ${(CONFIG.underAttackReserve * 100).toFixed(0)}%`;
          status = "DEFENSIVE";
          riskLevel = "HIGH";
          STATE.recommendedAction = 'DEFEND';
          STATE.recommendedReason = 'Counter-attack while maintaining reserves';
        } else {
          targetSlider = 0; // Will be converted to 1% by game minimum
          reason = `🛡️ DEFENDING - Building reserves`;
          status = "DEFENSIVE";
          riskLevel = "CRITICAL";
          STATE.recommendedAction = 'WAIT';
          STATE.recommendedReason = 'Build strength before counter-attacking';
        }
      }
      // Too many troops
      else if (currentRatio > optimalRatio + tolerance) {
        const targetTroops = optimalRatio * maxTroops;
        const excessTroops = troops - targetTroops;

        let rawSlider = (excessTroops / troops) * 100;

        const overage = currentRatio - optimalRatio;
        if (overage > CONFIG.aggressiveThreshold) {
          const boostFactor = 1 + Math.min(0.3, overage * 0.5);
          rawSlider = rawSlider * boostFactor;
        }

        targetSlider = Math.max(minAttack * 100, Math.min(80, rawSlider));

        reason = isEarly ?
          `🚀 EARLY EXPANSION: ${(currentRatio * 100).toFixed(1)}% > ${(optimalRatio * 100).toFixed(1)}%` :
          `Excess troops: ${(currentRatio * 100).toFixed(1)}% > ${(optimalRatio * 100).toFixed(1)}%`;
        status = isEarly ? "EXPANDING" : "EXCESS";
        riskLevel = "LOW";
        STATE.recommendedAction = 'ATTACK';
        STATE.recommendedReason = isEarly ? 'Claim territory while weak' : 'Attack weak targets to optimize growth';
      }
      // Too few troops
      else if (currentRatio < optimalRatio - tolerance) {
        if (isEarly && currentRatio > 0.10) {
          // In early game, still attack even when "growing" if we have reasonable troops
          targetSlider = Math.max(minAttack * 100, 10); // At least 10% in early game
          reason = `🌱 EARLY GROWTH: ${(currentRatio * 100).toFixed(1)}% < ${(optimalRatio * 100).toFixed(1)}% but attacking anyway`;
          status = "EARLY_GROWTH";
          riskLevel = "MEDIUM";
          STATE.recommendedAction = 'ATTACK';
          STATE.recommendedReason = 'Expand while enemies are weak';
        } else {
          targetSlider = 0; // Will be converted to 1% by game minimum
          reason = `Growing: ${(currentRatio * 100).toFixed(1)}% < ${(optimalRatio * 100).toFixed(1)}%`;
          status = "GROWING";
          riskLevel = "MEDIUM";
          STATE.recommendedAction = 'WAIT';
          STATE.recommendedReason = 'Let population grow to optimal level';
        }
      }
      // Optimal range
      else {
        if (isEarly) {
          // In early game, even "optimal" should still attack for expansion
          targetSlider = Math.max(minAttack * 100, 15); // At least 15% in early game
          reason = `🎯 EARLY OPTIMAL: ${(currentRatio * 100).toFixed(1)}% ≈ ${(optimalRatio * 100).toFixed(1)}% - expanding`;
          status = "EARLY_OPTIMAL";
          riskLevel = "LOW";
          STATE.recommendedAction = 'ATTACK';
          STATE.recommendedReason = 'Expand while competition is low';
        } else {
          targetSlider = 0; // Will be converted to 1% by game minimum
          reason = `Optimal: ${(currentRatio * 100).toFixed(1)}% ≈ ${(optimalRatio * 100).toFixed(1)}%`;
          status = "OPTIMAL";
          riskLevel = "LOW";
          STATE.recommendedAction = 'MAINTAIN';
          STATE.recommendedReason = 'Maintain optimal growth rate';
        }
      }

      // Final safety check (use dynamic minAttack)
      let finalSlider = Math.round(targetSlider);
      if (finalSlider > 0 && finalSlider < minAttack * 100) {
        finalSlider = Math.round(minAttack * 100);
      }

      return {
        currentRatio,
        optimalRatio,
        targetSlider: finalSlider,
        reason,
        status,
        riskLevel,
        underAttack,
        troops,
        maxTroops
      };
    } catch (error) {
      console.error("[Math] Error calculating optimal slider:", error);
      return null;
    }
  }

  /* ===== PERFORMANCE TRACKING ===== */
  function updatePerformanceStats(api) {
    if (!CONFIG.trackPerformance) return;

    const { myPlayer } = api;
    const now = Date.now();

    if (now - STATE.lastStatsUpdate < CONFIG.statsUpdateInterval) return;
    STATE.lastStatsUpdate = now;

    const currentTroops = myPlayer.troops();
    const currentTerritories = myPlayer.numTilesOwned();

    STATE.stats.currentTroops = currentTroops;
    STATE.stats.currentTerritories = currentTerritories;

    if (currentTroops > STATE.stats.peakTroops) {
      STATE.stats.peakTroops = currentTroops;
    }

    STATE.stats.totalGrowth = currentTroops - STATE.stats.startTroops;

    if (currentTerritories > STATE.stats.startTerritories) {
      STATE.stats.territoriesGained = currentTerritories - STATE.stats.startTerritories;
    } else if (currentTerritories < STATE.stats.startTerritories) {
      STATE.stats.territoriesLost = STATE.stats.startTerritories - currentTerritories;
    }
  }

  /* ===== MAIN LOOP ===== */
  let currentTarget = [];
  const TARGET_UPDATE_INTERVAL = 3000;
  let lastTargetUpdate = 0;

  async function mainLoop() {
    const api = getGameAPI();
    if (!api) return;

    const now = Date.now();

    // Update neighbors
    if (now - STATE.lastUpdate > TARGET_UPDATE_INTERVAL) {
      await scanNeighbors(api);
      STATE.lastUpdate = now;
    }

    // Update threats
    if (now - STATE.lastThreatUpdate > CONFIG.threatUpdateInterval) {
      detectThreats(api);
      STATE.lastThreatUpdate = now;
    }

    // Update targets
    if (now - lastTargetUpdate > TARGET_UPDATE_INTERVAL) {
      currentTarget = await findAllTargets(api);
      lastTargetUpdate = now;
    }

    // Update stats
    updatePerformanceStats(api);

    // Calculate optimal slider
    const calc = calculateOptimalSlider(api);
    if (calc) {
      updateUI(calc, currentTarget);
    }

    // Update diplomacy analysis (async)
    analyzeDiplomacy(api).then(diplomacy => {
      updateDiplomacyUI(diplomacy);
    }).catch(error => {
      console.warn('[Diplomacy] Analysis failed:', error);
      updateDiplomacyUI({ alliances: [], threats: [], opportunities: [], recommendations: ['Analysis failed - check console'] });
    });

    // Execute bot actions if enabled
    if (CONFIG.enabled && CONFIG.botMode !== 'manual') {
      if (STATE.isFirstRun) {
        STATE.isFirstRun = false;
        console.log("[Bot] 🤖 Autopilot enabled");
      }

      const currentSlider = Math.round((api.uiState?.attackRatio || 0) * 100);

      if (Math.abs(currentSlider - calc.targetSlider) > 2) {
        setSliderValue(calc.targetSlider);
      }
    }
  }

  /* ===== UI CREATION ===== */
  function createUI() {
    const widget = document.createElement('div');
    widget.id = 'new-player-bot-widget';
    widget.style.cssText = `
      position: fixed;
      top: ${CONFIG.position.y}px;
      left: ${CONFIG.position.x}px;
      background: linear-gradient(135deg, rgba(15,23,42,0.98), rgba(30,41,59,0.98));
      border: 2px solid rgba(59,130,246,0.6);
      border-radius: 16px;
      color: #f1f5f9;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
      font-size: 13px;
      z-index: 999999;
      min-width: 380px;
      max-width: 600px;
      width: ${CONFIG.widgetSize?.width || 420}px;
      height: ${CONFIG.widgetSize?.height || 'auto'};
      box-shadow: 0 25px 50px -12px rgba(0,0,0,0.6), 0 0 25px rgba(59,130,246,0.4);
      backdrop-filter: blur(12px);
      user-select: none;
      resize: both;
      overflow: hidden;
    `;

    widget.innerHTML = `
      <div id="widget-header" style="
        padding: 14px 18px;
        background: rgba(59,130,246,0.15);
        border-bottom: 1px solid rgba(59,130,246,0.4);
        cursor: move;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-radius: 14px 14px 0 0;
        flex-shrink: 0;
      ">
        <div style="display: flex; align-items: center; gap: 10px;">
          <span style="font-size: 20px;">🤖</span>
          <div>
            <div style="font-weight: 700; color: #60a5fa; font-size: 15px;">New Player Bot Assistant</div>
            <div style="font-size: 10px; color: #94a3b8; margin-top: 2px;">v2.0 - AI-Powered Gameplay</div>
          </div>
        </div>
        <button id="collapse-btn" style="
          background: rgba(59,130,246,0.2);
          border: 1px solid rgba(59,130,246,0.3);
          color: #94a3b8;
          cursor: pointer;
          font-size: 18px;
          padding: 4px 12px;
          border-radius: 6px;
          transition: all 0.2s;
        ">${CONFIG.collapsed ? '▲' : '▼'}</button>
      </div>

      <div id="widget-content" style="
        padding: 16px;
        ${CONFIG.collapsed ? 'display: none;' : 'display: flex;'}
        overflow-y: auto;
        height: calc(100% - 70px);
        flex-direction: column;
        gap: 8px;
      ">
        <div id="cards-container" style="
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 12px;
          align-items: start;
        ">


        </div>
      </div>
    `;

    document.body.appendChild(widget);

    // Create and populate cards
    createCards(widget);

    return widget;
  }

  /* ===== CARD CREATION FUNCTIONS ===== */
  function createCollapsibleCard(id, title, icon, color, content, isExpanded = true) {
    const cardId = `card-${id}`;
    const contentId = `content-${id}`;
    const toggleId = `toggle-${id}`;

    return `
      <div id="${cardId}" class="widget-card" style="
        background: rgba(${color}, 0.1);
        border: 1px solid rgba(${color}, 0.3);
        border-radius: 8px;
        overflow: hidden;
        transition: all 0.3s ease;
      ">
        <div class="card-header" style="
          padding: 10px 12px;
          background: rgba(${color}, 0.05);
          border-bottom: 1px solid rgba(${color}, 0.2);
          cursor: pointer;
          display: flex;
          justify-content: space-between;
          align-items: center;
          user-select: none;
        " onclick="toggleCard('${id}')">
          <div style="display: flex; align-items: center; gap: 8px;">
            <span style="font-size: 12px;">${icon}</span>
            <span style="font-weight: 600; color: rgb(${color}); font-size: 11px;">${title}</span>
          </div>
          <span id="${toggleId}" style="
            color: #94a3b8;
            font-size: 14px;
            transition: transform 0.2s;
            transform: rotate(${isExpanded ? '0' : '180'}deg);
          ">▼</span>
        </div>
        <div id="${contentId}" class="card-content" style="
          padding: 12px;
          ${isExpanded ? '' : 'display: none;'}
        ">
          ${content}
        </div>
      </div>
    `;
  }

  function createCards(widget) {
    const container = widget.querySelector('#cards-container');
    if (!container) return;

    // Bot Controls Card
    const botControlsContent = `
      <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 10px;">
        <label style="display: flex; align-items: center; gap: 8px; cursor: pointer; font-weight: 600;">
          <input type="checkbox" id="enable-bot" ${CONFIG.enabled ? 'checked' : ''} style="
            cursor: pointer;
            width: 18px;
            height: 18px;
            accent-color: #10b981;
          ">
          <span style="color: #10b981;">Enable Bot Autopilot</span>
        </label>
      </div>

      <div style="margin-top: 10px;">
        <label style="display: block; margin-bottom: 6px; font-size: 11px; color: #94a3b8; text-transform: uppercase; font-weight: 600;">Bot Mode:</label>
        <select id="bot-mode" style="
          width: 100%;
          padding: 8px 10px;
          border-radius: 6px;
          background: rgba(0,0,0,0.4);
          color: white;
          border: 1px solid rgba(148,163,184,0.3);
          font-family: inherit;
          font-size: 13px;
          cursor: pointer;
        ">
          <option value="aggressive" ${CONFIG.botMode === 'aggressive' ? 'selected' : ''}>🔥 Aggressive - High risk, high reward</option>
          <option value="balanced" ${CONFIG.botMode === 'balanced' ? 'selected' : ''}>⚖️ Balanced - Optimal for new players</option>
          <option value="defensive" ${CONFIG.botMode === 'defensive' ? 'selected' : ''}>🛡️ Defensive - Conservative play</option>
          <option value="manual" ${CONFIG.botMode === 'manual' ? 'selected' : ''}>🎯 Manual - Recommendations only</option>
        </select>
      </div>
    `;

    // Slider Test Card
    const sliderTestContent = `
      <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 8px;">
        <span style="font-weight: 600; color: #f59e0b; font-size: 12px;">Test Status</span>
        <span id="slider-test-status" style="font-size: 11px; color: #94a3b8;">Ready</span>
      </div>
      <button id="test-slider-btn" style="
        width: 100%;
        padding: 8px;
        background: rgba(245,158,11,0.2);
        border: 1px solid rgba(245,158,11,0.4);
        color: #f59e0b;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        font-weight: 600;
        transition: all 0.2s;
      ">Test Slider Control</button>
    `;

    // Current Status Card
    const currentStatusContent = `
      <div id="status-display" style="font-size: 11px; line-height: 1.4;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
          <span style="color: #94a3b8;">Status:</span>
          <span id="current-status" style="color: #10b981;">INITIALIZING</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
          <span style="color: #94a3b8;">Current Ratio:</span>
          <span id="current-ratio">--</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
          <span style="color: #94a3b8;">Optimal Ratio:</span>
          <span id="optimal-ratio">42%</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
          <span style="color: #94a3b8;">Attack Slider:</span>
          <span id="attack-slider">0%</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
          <span style="color: #94a3b8;">Risk Level:</span>
          <span id="risk-level">LOW</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span style="color: #94a3b8;">Threats:</span>
          <span id="threat-count">0</span>
        </div>
      </div>
    `;

    // Recommendations Card
    const recommendationsContent = `
      <div style="font-size: 11px; line-height: 1.4;">
        <div style="margin-bottom: 6px;">
          <span style="color: #94a3b8;">Action:</span>
          <span id="recommended-action" style="color: #f59e0b; font-weight: 600; margin-left: 8px;">WAIT</span>
        </div>
        <div id="recommended-reason" style="color: #cbd5e1; font-style: italic;">Initializing bot systems...</div>
      </div>
    `;

    // Target Analysis Card
    const targetAnalysisContent = `
      <div id="target-list" style="font-size: 10px; max-height: 120px; overflow-y: auto;">
        <div style="color: #94a3b8; text-align: center; padding: 20px;">Scanning for targets...</div>
      </div>
    `;

    // Diplomacy Card
    const diplomacyContent = `
      <div id="diplomacy-analysis" style="font-size: 10px; line-height: 1.3; max-height: 140px; overflow-y: auto;">
        <div style="margin-bottom: 8px;">
          <div style="color: #22c55e; font-weight: 600; margin-bottom: 4px;">🤝 Alliances (<span id="alliance-count">0</span>)</div>
          <div id="alliance-list" style="color: #cbd5e1; font-size: 9px;">No alliances</div>
        </div>
        <div style="margin-bottom: 8px;">
          <div style="color: #ef4444; font-weight: 600; margin-bottom: 4px;">⚠️ Threats (<span id="threat-count-diplo">0</span>)</div>
          <div id="threat-list" style="color: #cbd5e1; font-size: 9px;">No threats</div>
        </div>
        <div style="margin-bottom: 8px;">
          <div style="color: #f59e0b; font-weight: 600; margin-bottom: 4px;">🎯 Opportunities (<span id="opportunity-count">0</span>)</div>
          <div id="opportunity-list" style="color: #cbd5e1; font-size: 9px;">No opportunities</div>
        </div>
        <div>
          <div style="color: #8b5cf6; font-weight: 600; margin-bottom: 4px;">💭 Recommendations</div>
          <div id="diplomacy-recommendations" style="color: #cbd5e1; font-size: 9px;">Analyzing...</div>
        </div>
      </div>
    `;

    // Performance Card
    const performanceContent = `
      <div id="performance-stats" style="font-size: 10px; line-height: 1.4;">
        <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
          <span style="color: #94a3b8;">Session Time:</span>
          <span id="session-time">0:00</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
          <span style="color: #94a3b8;">Troop Growth:</span>
          <span id="troop-growth">+0</span>
        </div>
        <div style="display: flex; justify-content: space-between; margin-bottom: 3px;">
          <span style="color: #94a3b8;">Territory Change:</span>
          <span id="territory-change">+0</span>
        </div>
        <div style="display: flex; justify-content: space-between;">
          <span style="color: #94a3b8;">Peak Troops:</span>
          <span id="peak-troops">0</span>
        </div>
      </div>
    `;

    // Create all cards
    const cards = [
      { id: 'botControls', title: 'BOT CONTROLS', icon: '🤖', color: '16,185,129', content: botControlsContent },
      { id: 'currentStatus', title: 'CURRENT STATUS', icon: '📊', color: '99,102,241', content: currentStatusContent },
      { id: 'recommendations', title: 'RECOMMENDATION', icon: '💡', color: '139,69,19', content: recommendationsContent },
      { id: 'targetAnalysis', title: 'TARGET ANALYSIS', icon: '🎯', color: '220,38,127', content: targetAnalysisContent },
      { id: 'diplomacy', title: 'DIPLOMACY', icon: '🤝', color: '168,85,247', content: diplomacyContent },
      { id: 'performance', title: 'PERFORMANCE', icon: '📈', color: '34,197,94', content: performanceContent },
      { id: 'sliderTest', title: 'SLIDER TEST', icon: '🧪', color: '245,158,11', content: sliderTestContent }
    ];

    container.innerHTML = cards.map(card =>
      createCollapsibleCard(
        card.id,
        card.title,
        card.icon,
        card.color,
        card.content,
        CONFIG.cardStates[card.id] !== false
      )
    ).join('');

    // Add hover effects to card headers
    cards.forEach(card => {
      const header = container.querySelector(`#card-${card.id} .card-header`);
      if (header) {
        header.addEventListener('mouseenter', () => {
          header.style.background = `rgba(${card.color}, 0.1)`;
        });
        header.addEventListener('mouseleave', () => {
          header.style.background = `rgba(${card.color}, 0.05)`;
        });
      }
    });
  }

  /* ===== CARD TOGGLE FUNCTIONALITY ===== */
  function toggleCard(cardId) {
    const content = document.getElementById(`content-${cardId}`);
    const toggle = document.getElementById(`toggle-${cardId}`);

    if (!content || !toggle) return;

    const isExpanded = content.style.display !== 'none';

    if (isExpanded) {
      content.style.display = 'none';
      toggle.style.transform = 'rotate(180deg)';
      CONFIG.cardStates[cardId] = false;
    } else {
      content.style.display = 'block';
      toggle.style.transform = 'rotate(0deg)';
      CONFIG.cardStates[cardId] = true;
    }

    saveConfig();
  }

  // Make toggleCard available globally for onclick handlers
  window.toggleCard = toggleCard;

  /* ===== UI UPDATE FUNCTION ===== */
  function updateUI(calc, targets) {
    // Update status display
    const statusEl = document.getElementById('current-status');
    const currentRatioEl = document.getElementById('current-ratio');
    const optimalRatioEl = document.getElementById('optimal-ratio');
    const attackSliderEl = document.getElementById('attack-slider');
    const riskLevelEl = document.getElementById('risk-level');
    const threatCountEl = document.getElementById('threat-count');

    if (statusEl) {
      statusEl.textContent = calc.status;
      statusEl.style.color = getStatusColor(calc.status);
    }

    if (currentRatioEl) {
      currentRatioEl.textContent = `${(calc.currentRatio * 100).toFixed(1)}%`;
      currentRatioEl.style.color = calc.currentRatio > calc.optimalRatio ? '#f59e0b' : '#10b981';
    }

    if (optimalRatioEl) {
      optimalRatioEl.textContent = `${(calc.optimalRatio * 100).toFixed(1)}%`;
    }

    if (attackSliderEl) {
      attackSliderEl.textContent = `${calc.targetSlider}%`;
      attackSliderEl.style.color = calc.targetSlider > 0 ? '#ef4444' : '#94a3b8';
    }

    if (riskLevelEl) {
      riskLevelEl.textContent = calc.riskLevel;
      riskLevelEl.style.color = getRiskColor(calc.riskLevel);
    }

    if (threatCountEl) {
      threatCountEl.textContent = STATE.threats.length.toString();
      threatCountEl.style.color = STATE.threats.length > 0 ? '#ef4444' : '#10b981';
    }

    // Update recommendations
    const actionEl = document.getElementById('recommended-action');
    const reasonEl = document.getElementById('recommended-reason');

    if (actionEl) {
      actionEl.textContent = STATE.recommendedAction;
      actionEl.style.color = getActionColor(STATE.recommendedAction);
    }

    if (reasonEl) {
      reasonEl.textContent = STATE.recommendedReason;
    }

    // Update target list
    updateTargetList(targets);

    // Update performance stats
    if (CONFIG.trackPerformance) {
      updatePerformanceDisplay();
    }
  }

  function getStatusColor(status) {
    switch (status) {
      case 'CRITICAL': return '#ef4444';
      case 'DEFENSIVE': return '#f59e0b';
      case 'GROWING': return '#3b82f6';
      case 'OPTIMAL': return '#10b981';
      case 'EXCESS': return '#f59e0b';
      default: return '#94a3b8';
    }
  }

  function getRiskColor(risk) {
    switch (risk) {
      case 'CRITICAL': return '#ef4444';
      case 'HIGH': return '#f59e0b';
      case 'MEDIUM': return '#3b82f6';
      case 'LOW': return '#10b981';
      default: return '#94a3b8';
    }
  }

  function getActionColor(action) {
    switch (action) {
      case 'ATTACK': return '#ef4444';
      case 'DEFEND': return '#f59e0b';
      case 'WAIT': return '#3b82f6';
      case 'MAINTAIN': return '#10b981';
      default: return '#94a3b8';
    }
  }

  /* ===== DIPLOMACY UI UPDATE ===== */
  function updateDiplomacyUI(diplomacy) {
    // Update alliance count and list
    const allianceCountEl = document.getElementById('alliance-count');
    const allianceListEl = document.getElementById('alliance-list');

    if (allianceCountEl) allianceCountEl.textContent = diplomacy.alliances.length;
    if (allianceListEl) {
      if (diplomacy.alliances.length === 0) {
        allianceListEl.innerHTML = 'No alliances';
      } else {
        allianceListEl.innerHTML = diplomacy.alliances.map(ally =>
          `<div style="margin-bottom: 2px;">
            <span style="color: #22c55e;">${ally.name}</span>
            <span style="color: #94a3b8; font-size: 8px;"> (${ally.status})</span>
          </div>`
        ).join('');
      }
    }

    // Update threat count and list
    const threatCountEl = document.getElementById('threat-count-diplo');
    const threatListEl = document.getElementById('threat-list');

    if (threatCountEl) threatCountEl.textContent = diplomacy.threats.length;
    if (threatListEl) {
      if (diplomacy.threats.length === 0) {
        threatListEl.innerHTML = 'No threats';
      } else {
        threatListEl.innerHTML = diplomacy.threats.map(threat =>
          `<div style="margin-bottom: 3px; padding: 2px; background: rgba(239,68,68,0.1); border-radius: 3px;">
            <div style="color: #ef4444; font-weight: 600;">${threat.name}</div>
            <div style="color: #94a3b8; font-size: 8px;">
              ${threat.threatLevel} threat • ${threat.relation} • ${threat.type}
            </div>
          </div>`
        ).join('');
      }
    }

    // Update opportunity count and list
    const opportunityCountEl = document.getElementById('opportunity-count');
    const opportunityListEl = document.getElementById('opportunity-list');

    if (opportunityCountEl) opportunityCountEl.textContent = diplomacy.opportunities.length;
    if (opportunityListEl) {
      if (diplomacy.opportunities.length === 0) {
        opportunityListEl.innerHTML = 'No opportunities';
      } else {
        opportunityListEl.innerHTML = diplomacy.opportunities.map(opp =>
          `<div style="margin-bottom: 3px; padding: 2px; background: rgba(245,158,11,0.1); border-radius: 3px;">
            <div style="color: #f59e0b; font-weight: 600;">${opp.name}</div>
            <div style="color: #94a3b8; font-size: 8px;">
              ${opp.weakness} (${opp.weaknessRatio}) • ${opp.relation} • ${opp.type}
            </div>
          </div>`
        ).join('');
      }
    }

    // Update recommendations
    const recommendationsEl = document.getElementById('diplomacy-recommendations');
    if (recommendationsEl) {
      if (diplomacy.recommendations.length === 0) {
        recommendationsEl.innerHTML = 'All good!';
      } else {
        recommendationsEl.innerHTML = diplomacy.recommendations.join('<br>');
      }
    }
  }

  /* ===== TARGET LIST UPDATE ===== */
  function updateTargetList(targets) {
    const targetListEl = document.getElementById('target-list');
    if (!targetListEl || !targets || targets.length === 0) {
      if (targetListEl) {
        targetListEl.innerHTML = '<div style="color: #94a3b8; text-align: center; padding: 20px;">No targets found</div>';
      }
      return;
    }

    const targetHTML = targets.slice(0, 5).map(target => {
      const winChanceColor = target.winChance > 0.7 ? '#10b981' : target.winChance > 0.4 ? '#f59e0b' : '#ef4444';
      const threatColor = target.threat === 'HIGH' ? '#ef4444' : target.threat === 'MEDIUM' ? '#f59e0b' : '#10b981';

      return `
        <div style="
          background: rgba(0,0,0,0.2);
          border-radius: 4px;
          padding: 8px;
          margin-bottom: 6px;
          border-left: 3px solid ${winChanceColor};
        ">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
            <span style="font-weight: 600; color: #f1f5f9;">${target.targetName}</span>
            <span style="color: ${winChanceColor};">${target.momentum}</span>
          </div>
          <div style="display: flex; justify-content: space-between; font-size: 9px; color: #94a3b8;">
            <span>Win: <span style="color: ${winChanceColor};">${(target.winChance * 100).toFixed(0)}%</span></span>
            <span>Ratio: <span style="color: #f59e0b;">${(target.recommendedRatio * 100).toFixed(0)}%</span></span>
            <span>Threat: <span style="color: ${threatColor};">${target.threat}</span></span>
          </div>
        </div>
      `;
    }).join('');

    targetListEl.innerHTML = targetHTML;
  }

  /* ===== PERFORMANCE DISPLAY UPDATE ===== */
  function updatePerformanceDisplay() {
    const sessionTimeEl = document.getElementById('session-time');
    const troopGrowthEl = document.getElementById('troop-growth');
    const territoryChangeEl = document.getElementById('territory-change');
    const peakTroopsEl = document.getElementById('peak-troops');

    if (sessionTimeEl) {
      const elapsed = Math.floor((Date.now() - STATE.stats.startTime) / 1000);
      const minutes = Math.floor(elapsed / 60);
      const seconds = elapsed % 60;
      sessionTimeEl.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }

    if (troopGrowthEl) {
      const growth = STATE.stats.totalGrowth;
      troopGrowthEl.textContent = growth >= 0 ? `+${growth.toLocaleString()}` : growth.toLocaleString();
      troopGrowthEl.style.color = growth >= 0 ? '#10b981' : '#ef4444';
    }

    if (territoryChangeEl) {
      const gained = STATE.stats.territoriesGained;
      const lost = STATE.stats.territoriesLost;
      const net = gained - lost;
      territoryChangeEl.textContent = net >= 0 ? `+${net}` : net.toString();
      territoryChangeEl.style.color = net >= 0 ? '#10b981' : '#ef4444';
    }

    if (peakTroopsEl) {
      peakTroopsEl.textContent = STATE.stats.peakTroops.toLocaleString();
    }
  }

  /* ===== SLIDER TESTING ===== */
  function testSliderControl() {
    const statusEl = document.getElementById('slider-test-status');
    const testBtn = document.getElementById('test-slider-btn');

    if (!statusEl || !testBtn) return;

    statusEl.textContent = 'Testing...';
    statusEl.style.color = '#f59e0b';
    testBtn.disabled = true;
    testBtn.style.opacity = '0.5';

    console.log("[Test] 🧪 Starting slider control test");

    const testSequence = [0, 25, 50, 25, 0];
    let currentStep = 0;

    function runNextStep() {
      if (currentStep >= testSequence.length) {
        statusEl.textContent = 'Test Complete ✅';
        statusEl.style.color = '#10b981';
        testBtn.disabled = false;
        testBtn.style.opacity = '1';
        console.log("[Test] ✅ Slider test completed successfully");

        setTimeout(() => {
          statusEl.textContent = 'Ready';
          statusEl.style.color = '#94a3b8';
        }, 3000);
        return;
      }

      const targetValue = testSequence[currentStep];
      console.log(`[Test] Setting slider to ${targetValue}%`);

      const success = setSliderValue(targetValue);
      if (!success) {
        statusEl.textContent = 'Test Failed ❌';
        statusEl.style.color = '#ef4444';
        testBtn.disabled = false;
        testBtn.style.opacity = '1';
        console.error("[Test] ❌ Slider test failed");

        setTimeout(() => {
          statusEl.textContent = 'Ready';
          statusEl.style.color = '#94a3b8';
        }, 3000);
        return;
      }

      currentStep++;
      setTimeout(runNextStep, 800);
    }

    runNextStep();
  }

  /* ===== DRAG AND RESIZE FUNCTIONALITY ===== */
  function makeDraggable(widget) {
    const header = widget.querySelector('#widget-header');
    if (!header) return;

    let isDragging = false;
    let startX, startY, startLeft, startTop;

    function onMouseDown(e) {
      // Don't drag if clicking on buttons
      if (e.target.tagName === 'BUTTON') return;

      isDragging = true;
      startX = e.clientX;
      startY = e.clientY;
      startLeft = parseInt(widget.style.left) || CONFIG.position.x;
      startTop = parseInt(widget.style.top) || CONFIG.position.y;

      header.style.cursor = 'grabbing';
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
      e.preventDefault();
    }

    function onMouseMove(e) {
      if (!isDragging) return;

      const deltaX = e.clientX - startX;
      const deltaY = e.clientY - startY;

      const newLeft = Math.max(0, Math.min(window.innerWidth - widget.offsetWidth, startLeft + deltaX));
      const newTop = Math.max(0, Math.min(window.innerHeight - widget.offsetHeight, startTop + deltaY));

      widget.style.left = newLeft + 'px';
      widget.style.top = newTop + 'px';
    }

    function onMouseUp() {
      if (!isDragging) return;

      isDragging = false;
      header.style.cursor = 'move';

      // Save position
      CONFIG.position.x = parseInt(widget.style.left);
      CONFIG.position.y = parseInt(widget.style.top);
      saveConfig();

      document.removeEventListener('mousemove', onMouseMove);
      document.removeEventListener('mouseup', onMouseUp);
    }

    header.addEventListener('mousedown', onMouseDown);
  }

  function makeResizable(widget) {
    // Add resize observer to save size changes
    if (window.ResizeObserver) {
      const resizeObserver = new ResizeObserver(entries => {
        for (const entry of entries) {
          const { width, height } = entry.contentRect;
          CONFIG.widgetSize.width = Math.max(380, width);
          CONFIG.widgetSize.height = height;
          saveConfig();

          // Update grid layout based on new width
          updateGridLayout(widget);
        }
      });

      resizeObserver.observe(widget);
    }
  }

  function updateGridLayout(widget) {
    const container = widget.querySelector('#cards-container');
    if (!container) return;

    const widgetWidth = widget.offsetWidth;
    const cardMinWidth = 300;
    const padding = 32; // Account for padding
    const gap = 12;

    let columns = Math.floor((widgetWidth - padding) / (cardMinWidth + gap));
    columns = Math.max(1, columns);

    container.style.gridTemplateColumns = `repeat(${columns}, 1fr)`;
  }

  /* ===== EVENT HANDLERS ===== */
  function setupEventHandlers(widget) {
    // Enable bot checkbox
    const enableBotCheckbox = widget.querySelector('#enable-bot');
    if (enableBotCheckbox) {
      enableBotCheckbox.addEventListener('change', (e) => {
        CONFIG.enabled = e.target.checked;
        saveConfig();
        console.log(`[Bot] ${CONFIG.enabled ? 'Enabled' : 'Disabled'} autopilot`);

        if (CONFIG.enabled) {
          console.log("[Bot] 🤖 Autopilot activated");
        } else {
          console.log("[Bot] ⏸️ Autopilot paused");
        }
      });
    }

    // Bot mode selector
    const botModeSelect = widget.querySelector('#bot-mode');
    if (botModeSelect) {
      botModeSelect.addEventListener('change', (e) => {
        const newMode = e.target.value;
        CONFIG.botMode = newMode;
        applyMode(newMode);
        saveConfig();
        console.log(`[Bot] Mode changed to: ${newMode}`);
      });
    }

    // Collapse button
    const collapseBtn = widget.querySelector('#collapse-btn');
    const widgetContent = widget.querySelector('#widget-content');
    if (collapseBtn && widgetContent) {
      collapseBtn.addEventListener('click', () => {
        CONFIG.collapsed = !CONFIG.collapsed;
        widgetContent.style.display = CONFIG.collapsed ? 'none' : 'flex';
        collapseBtn.textContent = CONFIG.collapsed ? '▲' : '▼';
        saveConfig();

        // Update grid layout after collapse/expand
        if (!CONFIG.collapsed) {
          setTimeout(() => updateGridLayout(widget), 100);
        }
      });
    }

    // Test slider button
    const testSliderBtn = widget.querySelector('#test-slider-btn');
    if (testSliderBtn) {
      testSliderBtn.addEventListener('click', testSliderControl);
    }

    // Make widget draggable and resizable
    makeDraggable(widget);
    makeResizable(widget);

    // Initial grid layout update
    setTimeout(() => updateGridLayout(widget), 100);
  }

  /* ===== INITIALIZATION ===== */
  function initializeBot() {
    console.log("[Init] 🚀 Initializing New Player Bot Assistant...");

    // Apply current mode settings
    applyMode(CONFIG.botMode);

    // Create and setup UI
    const widget = createUI();
    setupEventHandlers(widget);

    // Start main loop
    const mainLoopInterval = setInterval(mainLoop, 1000);

    // Run startup slider test if enabled
    setTimeout(() => {
      if (CONFIG.enabled) {
        console.log("[Init] 🧪 Running startup slider test...");
        testSliderControl();
      }
    }, 2000);

    console.log("[Init] ✅ Bot initialization complete!");
    console.log("[Init] 📖 Check the README.md for usage instructions");

    return {
      widget,
      mainLoopInterval,
      stop: () => {
        clearInterval(mainLoopInterval);
        widget.remove();
        console.log("[Bot] 🛑 Bot stopped");
      }
    };
  }

  /* ===== STARTUP ===== */
  function waitForGameAndStart() {
    const checkInterval = setInterval(() => {
      const api = getGameAPI();
      if (api) {
        clearInterval(checkInterval);
        console.log("[Startup] 🎮 Game detected, starting bot...");

        // Initialize bot
        const botInstance = initializeBot();

        // Store bot instance globally for debugging
        window.newPlayerBot = botInstance;

      } else {
        console.log("[Startup] ⏳ Waiting for game to load...");
      }
    }, 2000);
  }

  // Start when page is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', waitForGameAndStart);
  } else {
    waitForGameAndStart();
  }

})();
