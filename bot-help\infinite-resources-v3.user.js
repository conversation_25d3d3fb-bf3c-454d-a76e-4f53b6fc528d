// ==UserScript==
// @name         OpenFront.io - Infinite Resources v3 (Multiplayer Fix)
// @namespace    http://tampermonkey.net/
// @version      3.0
// @description  Client-side infinite resources for multiplayer games
// <AUTHOR>
// @match        https://openfront.io/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';
    
    console.log('🚀 Infinite Resources v3 (Multiplayer Fix) Loading...');
    
    let yourClientID = null;
    let gameInstance = null;
    
    // Method 1: Capture your client ID when joining
    function captureClientID() {
        const originalCustomEvent = CustomEvent;
        window.CustomEvent = function(type, eventInitDict) {
            if (type === 'join-lobby' && eventInitDict?.detail?.clientID) {
                yourClientID = eventInitDict.detail.clientID;
                console.log('🆔 Captured your client ID:', yourClientID);
            }
            return new originalCustomEvent(type, eventInitDict);
        };
    }
    
    // Method 2: Intercept WebSocket messages and modify YOUR player data
    function interceptWebSocket() {
        const originalWebSocket = window.WebSocket;
        window.WebSocket = function(url, protocols) {
            console.log('🔌 Creating WebSocket connection to:', url);
            const ws = new originalWebSocket(url, protocols);
            
            const originalOnMessage = ws.onmessage;
            ws.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    console.log('📨 WebSocket message type:', data.type);
                    
                    // Intercept start message
                    if (data.type === 'start' && data.gameStartInfo) {
                        console.log('🎯 Game starting, modifying your player resources');
                        
                        // Modify your player's resources in the start data
                        if (data.gameStartInfo.players && yourClientID) {
                            data.gameStartInfo.players.forEach(player => {
                                if (player.clientID === yourClientID) {
                                    console.log('💰 Found your player, setting infinite resources');
                                    player.gold = 999999;
                                    player.troops = 999999;
                                    player.resources = player.resources || {};
                                    player.resources.gold = 999999;
                                    player.resources.troops = 999999;
                                }
                            });
                        }
                        
                        // Create modified event
                        const modifiedEvent = new MessageEvent('message', {
                            data: JSON.stringify(data),
                            origin: event.origin,
                            source: event.source
                        });
                        
                        if (originalOnMessage) {
                            originalOnMessage.call(this, modifiedEvent);
                        }
                        return;
                    }
                    
                    // Intercept game updates
                    if (data.type === 'update' || data.type === 'turn') {
                        if (data.players && yourClientID) {
                            data.players.forEach(player => {
                                if (player.clientID === yourClientID) {
                                    // Ensure your resources stay infinite
                                    if (player.gold !== undefined) player.gold = 999999;
                                    if (player.troops !== undefined) player.troops = 999999;
                                    if (player.resources) {
                                        player.resources.gold = 999999;
                                        player.resources.troops = 999999;
                                    }
                                }
                            });
                        }
                        
                        // Create modified event
                        const modifiedEvent = new MessageEvent('message', {
                            data: JSON.stringify(data),
                            origin: event.origin,
                            source: event.source
                        });
                        
                        if (originalOnMessage) {
                            originalOnMessage.call(this, modifiedEvent);
                        }
                        return;
                    }
                    
                } catch (e) {
                    console.log('📨 Non-JSON WebSocket message or parsing error:', e);
                }
                
                // Pass through original message
                if (originalOnMessage) {
                    originalOnMessage.call(this, event);
                }
            };
            
            return ws;
        };
    }
    
    // Method 3: Find and hook into the game instance for real-time resource manipulation
    function hookGameInstance() {
        const findGame = () => {
            // Look for game instances in common locations
            const possibleGames = [
                window.game,
                window.gameInstance,
                window.currentGame,
                window.gameState
            ];
            
            for (const game of possibleGames) {
                if (game && (game.players || game.getPlayer)) {
                    console.log('🎮 Found game instance');
                    gameInstance = game;
                    setupResourceHooks(game);
                    return true;
                }
            }
            
            // Check for React components
            const reactRoot = document.querySelector('#root');
            if (reactRoot && reactRoot._reactInternalFiber) {
                console.log('🔍 Searching React components...');
                // Could traverse React component tree here
            }
            
            return false;
        };
        
        const setupResourceHooks = (game) => {
            console.log('🔧 Setting up real-time resource hooks');
            
            // Hook into resource getters if available
            if (game.getPlayer && yourClientID) {
                const originalGetPlayer = game.getPlayer;
                game.getPlayer = function(clientID) {
                    const player = originalGetPlayer.call(this, clientID);
                    if (clientID === yourClientID && player) {
                        // Override resource properties
                        if (player.gold !== undefined) player.gold = 999999;
                        if (player.troops !== undefined) player.troops = 999999;
                        if (player.resources) {
                            player.resources.gold = 999999;
                            player.resources.troops = 999999;
                        }
                    }
                    return player;
                };
            }
            
            // Set up periodic resource refresh
            setInterval(() => {
                if (game.players && yourClientID) {
                    game.players.forEach(player => {
                        if (player.clientID === yourClientID) {
                            if (player.gold !== undefined) player.gold = 999999;
                            if (player.troops !== undefined) player.troops = 999999;
                            if (player.resources) {
                                player.resources.gold = 999999;
                                player.resources.troops = 999999;
                            }
                        }
                    });
                }
            }, 1000);
        };
        
        // Keep trying to find the game
        const checkInterval = setInterval(() => {
            if (findGame()) {
                clearInterval(checkInterval);
            }
        }, 1000);
    }
    
    // Method 4: DOM manipulation to show infinite resources in UI
    function manipulateUI() {
        const updateResourceDisplay = () => {
            // Look for resource display elements
            const goldElements = document.querySelectorAll('[class*="gold"], [id*="gold"], .resource-gold');
            const troopElements = document.querySelectorAll('[class*="troop"], [id*="troop"], .resource-troops');
            
            goldElements.forEach(el => {
                if (el.textContent && el.textContent.match(/\d+/)) {
                    el.textContent = el.textContent.replace(/\d+/, '999999');
                }
            });
            
            troopElements.forEach(el => {
                if (el.textContent && el.textContent.match(/\d+/)) {
                    el.textContent = el.textContent.replace(/\d+/, '999999');
                }
            });
        };
        
        // Update UI periodically
        setInterval(updateResourceDisplay, 500);
    }
    
    // Apply all methods
    captureClientID();
    interceptWebSocket();
    
    // Wait a bit for the page to load, then start game hooks
    setTimeout(() => {
        hookGameInstance();
        manipulateUI();
    }, 2000);
    
    console.log('✅ Infinite Resources v3 hooks installed');
    
})();
