# Bot Behavior Analysis & Widget Improvements

## 🔍 Analysis of OpenFrontIO Bot Mechanics

### Key Insights from BotBehavior.test.ts and Game Code:

#### 1. **Bot Decision Making Patterns**
- **Trigger Ratios**: Bots use `triggerRatio`, `reserveRatio`, and `expandRatio` parameters
- **Enemy Selection**: Prioritizes neighboring bots with lowest troop density
- **Alliance Logic**: Nations (FakeHuman) are more reliable allies than players
- **Attack Patterns**: Bots attack Terra Nullius first, then weak neighbors

#### 2. **Nation vs Bot Differences**
- **Nations (PlayerType.FakeHuman)**: More strategic, better alliance partners
- **Bots (PlayerType.Bot)**: More aggressive, less reliable diplomatically
- **Starting Troops**: Nations get 2.5k-50k troops based on difficulty and strength
- **Attack Amount**: Bots attack with troops/20, others with troops/5

#### 3. **Alliance Mechanics**
- **Duration**: Alliances have expiration timers
- **Extension**: Nations extend based on relation (Friendly=always, Neutral=50% chance)
- **Betrayal**: Breaking alliances causes -200 relation with target, -40 with all others
- **Protection**: Allied players cannot attack each other (enforced by game)

#### 4. **Strategic Patterns**
- **Traitor Targeting**: Bots prioritize attacking marked traitors
- **Retaliation**: Bots switch enemies when under attack (largest attacker)
- **Ally Assistance**: Bots help allies attack their targets (if relation ≥ Friendly)
- **Border Priority**: Bots only attack neighbors they share borders with

## 🚀 Widget Improvements Implemented

### 1. **Fixed Early Game Attack Issue**
**Problem**: Bot was stuck at 1% attack ratio during crucial early expansion
**Solution**: 
- Added early game detection (first 5 minutes)
- Lowered critical threshold from 25% to 5% for early game
- Increased tolerance from 3% to 10% for early game
- Added aggressive expansion logic even in "optimal" range

### 2. **Fixed Widget Height Issue**
**Problem**: Widget opened too tall vertically on screen initially
**Solution**: Changed initial height from 'auto' to fixed 400px

### 3. **Added New Diplomacy Card**
**Features**:
- **Alliance Tracking**: Shows current allies with power ratios
- **Threat Analysis**: Identifies dangerous neighbors
- **Opportunity Detection**: Finds weak targets for expansion
- **Strategic Recommendations**: AI-powered diplomatic advice

## 🎯 New Diplomacy Features

### Alliance Intelligence
- Tracks ally strength relative to player
- Identifies "Much Stronger", "Balanced", "Weaker" allies
- Distinguishes between Nations (reliable) and Players (variable)

### Threat Assessment
- Calculates threat levels based on troop ratios
- Recommends alliances with dangerous neighbors
- Monitors non-allied neighbors for power changes

### Expansion Opportunities
- Identifies weak neighbors for potential attacks
- Shows weakness ratios (e.g., "2.3x weaker")
- Provides tactical recommendations

### Strategic Recommendations
- "Consider forming alliances for protection"
- "More threats than allies - seek diplomatic protection"
- "Weak neighbors detected - expansion opportunity"

## 🧠 Bot Behavior Insights for Players

### What Makes Nations Good Allies:
1. **Predictable**: Follow consistent behavioral patterns
2. **Loyal**: More likely to extend alliances (especially if Friendly)
3. **Strategic**: Use better decision-making algorithms
4. **Powerful**: Often start with more troops based on difficulty

### How to Exploit Bot Patterns:
1. **Early Aggression**: Attack before bots reach trigger ratios
2. **Density Targeting**: Bots target lowest density players first
3. **Border Control**: Bots can't attack without shared borders
4. **Alliance Timing**: Form alliances before becoming a threat

### Diplomatic Strategy:
1. **Nation Alliances**: Prioritize allying with strong nations
2. **Relation Management**: Maintain Friendly relations for better cooperation
3. **Betrayal Consequences**: Breaking alliances has severe reputation costs
4. **Timing**: Form alliances during peaceful periods, not during conflicts

## 📊 Technical Implementation Details

### Early Game Detection:
```javascript
function isEarlyGame() {
  return (Date.now() - STATE.gameStartTime) < CONFIG.earlyGameDuration; // 5 minutes
}
```

### Dynamic Configuration:
- **Early Game**: 25% optimal ratio, 10% tolerance, 5% critical threshold
- **Late Game**: 42% optimal ratio, 3% tolerance, 25% critical threshold

### Diplomacy Analysis:
- Real-time alliance monitoring
- Threat level calculations
- Opportunity scoring
- Strategic recommendation engine

## 🎮 Gameplay Impact

### Before Fixes:
- ❌ Stuck at 1% attack ratio in early game
- ❌ Missed crucial expansion opportunities
- ❌ Widget too tall, covering game view
- ❌ No diplomatic intelligence

### After Improvements:
- ✅ Aggressive early expansion (10-25% attack ratios)
- ✅ Smart territory claiming in first 5 minutes
- ✅ Compact, resizable widget interface
- ✅ Advanced diplomatic intelligence and recommendations
- ✅ Better understanding of bot behavior patterns

The widget now provides comprehensive strategic intelligence combining mathematical optimization with diplomatic awareness, giving players a significant advantage in understanding and exploiting game mechanics.
