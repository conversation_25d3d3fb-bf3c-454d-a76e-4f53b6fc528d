// ==UserScript==
// @name         OpenFrontIO - Auto Script V1 (Advanced Tactical Assistant)
// @namespace    http://tampermonkey.net/
// @version      1.0.0
// @description  Advanced tactical assistant with threat detection, smart attack ratios, border analysis, and visual overlays
// <AUTHOR>
// @match        https://openfront.io/*
// @grant        none
// ==/UserScript==

(function () {
  'use strict';

  console.log("╔════════════════════════════════════════════════════════════╗");
  console.log("║                                                            ║");
  console.log("║     🎯 Auto Script V1 (Advanced Tactical Assistant)       ║");
  console.log("║                                                            ║");
  console.log("╚════════════════════════════════════════════════════════════╝");

  /* ===== CONFIGURATION ===== */
  const CONFIG = {
    enabled: false,
    mode: 'auto', // 'auto' or 'manual'

    // Growth math constants (DYNAMIC - calculated based on maxTroops)
    baseOptimalRatio: 0.42,
    tolerance: 0.03,
    aggressiveThreshold: 0.15,

    // Risk management
    minAttackRatio: 0.15,
    underAttackReserve: 0.30,

    // Threat detection
    threatUpdateInterval: 3000,        // Check threats every 3s
    threatTroopThreshold: 5000,        // Alert if neighbor gains 5k+ troops
    threatRatioThreshold: 0.60,        // Alert if neighbor attack ratio > 60%

    // Smart attack ratios
    smartAttackRatios: true,
    weakTargetRatio: 0.10,             // 10% for very weak targets
    normalTargetRatio: 0.20,           // 20% for normal targets
    strongTargetRatio: 0.50,           // 50% for strong targets

    // Border analysis
    borderAnalysisEnabled: true,
    borderUpdateInterval: 5000,        // Update border analysis every 5s

    // Expansion opportunities
    expansionAnalysisEnabled: true,
    expansionUpdateInterval: 7000,     // Check expansion every 7s

    // Visual overlays
    visualOverlaysEnabled: true,
    showThreatIndicators: true,
    showOpportunityIndicators: true,
    showBorderPressure: true,

    // Manual mode
    manualSliderValue: 50,

    // Assisted mode behavior (prevents 1% trap in early game)
    assistedFloorRatio: 0.20,       // Keep at least 20% so clicks send troops when calc=0

    // UI state
    collapsed: false,
    position: { x: 20, y: 20 }
  };

  // Load saved config
  const savedConfig = localStorage.getItem('autoScriptV1_config');
  if (savedConfig) {
    try {
      Object.assign(CONFIG, JSON.parse(savedConfig));
    } catch (e) {
      console.warn("[Config] Failed to load saved config:", e);
    }
  }

  // Save config helper
  function saveConfig() {
    localStorage.setItem('autoScriptV1_config', JSON.stringify(CONFIG));
  }

  /* ===== STATE TRACKING ===== */
  const STATE = {
    // Neighbor tracking
    neighbors: new Map(), // neighborID -> { player, troops, tiles, density, lastUpdate, troopHistory }

    // Threat tracking
    threats: [],          // Array of { neighbor, threatLevel, reason, troops, ratio }

    // Border analysis
    borderData: new Map(), // neighborID -> { borderLength, pressure, vulnerability }

    // Expansion opportunities
    expansionTargets: [],  // Array of { direction, score, target, reason }

    // Attack history
    attackHistory: [],     // Track our attacks
    defenseHistory: [],    // Track attacks on us

    // Momentum tracking
    momentum: new Map(),   // neighborID -> { velocity, acceleration, trend }

    // Current optimal ratio (dynamic)
    currentOptimalRatio: 0.42,

    // Last update times
    lastThreatUpdate: 0,
    lastBorderUpdate: 0,
    lastExpansionUpdate: 0,
    lastNeighborUpdate: 0,

    // First run flag
    isFirstRun: true
  };

  /* ===== GAME API ACCESS ===== */
  let gameAPI = null;

  function getGameAPI() {
    if (gameAPI && gameAPI.isValid()) {
      return gameAPI;
    }

    const controlPanel = document.querySelector('control-panel');
    if (!controlPanel) return null;

    const game = controlPanel.game;
    const uiState = controlPanel.uiState;

    if (!game || !uiState) return null;

    const myPlayer = game.myPlayer();
    if (!myPlayer || !myPlayer.isAlive()) return null;

    gameAPI = {
      game,
      uiState,
      myPlayer,
      eventBus: controlPanel.eventBus,
      isValid: () => {
        const player = game.myPlayer();
        return player !== null && player.isAlive();
      }
    };

    console.log("[API] ✅ Game API initialized!");
    console.log("[API] Player:", myPlayer.name?.() || myPlayer.displayName?.() || "Unknown");

    return gameAPI;
  }

  /* ===== SLIDER CONTROL ===== */
  function setSliderValue(percentage) {
    const slider = document.querySelector('#attack-ratio');
    if (!slider) return false;

    try {
      // CRITICAL SAFETY: enforce minimum attack ratio
      let safePercentage = percentage;
      if (safePercentage > 0 && safePercentage < CONFIG.minAttackRatio * 100) {
        console.warn(`[SAFETY] Blocked ${safePercentage.toFixed(1)}% attack - enforcing minimum ${CONFIG.minAttackRatio * 100}%`);
        safePercentage = CONFIG.minAttackRatio * 100;
      }

      // Calculate target value
      const max = Number(slider.max || 100);
      const targetValue = Math.round(Math.max(0, Math.min(max, safePercentage)));

      // Only update if changed
      if (Math.abs(Number(slider.value) - targetValue) < 1) {
        return true;
      }

      // Batch DOM write using requestAnimationFrame
      requestAnimationFrame(() => {
        slider.value = String(targetValue);
        slider.dispatchEvent(new Event('input', { bubbles: true }));
        slider.dispatchEvent(new Event('change', { bubbles: true }));

        // Fallback: manually update visual fill track
        const fillTrack = slider.closest('.relative')?.querySelector('.bg-red-500\\/60');
        if (fillTrack) {
          fillTrack.style.width = `${(targetValue / max) * 100}%`;
        }
      });

      return true;
    } catch (error) {
      console.error("[Slider] Error setting value:", error);
      return false;
    }
  }

  /* ===== ATTACK DETECTION ===== */
  function isUnderAttack(api) {
    try {
      const { myPlayer } = api;
      const incomingAttacks = myPlayer.incomingAttacks?.() || [];
      return incomingAttacks.length > 0;
    } catch (error) {
      return false;
    }
  }

  function detectAttackChanges(api) {
    try {
      const { myPlayer } = api;
      const incomingAttacks = myPlayer.incomingAttacks?.() || [];

      // Check for new attacks
      const currentAttackIDs = new Set(incomingAttacks.map(a => a.id()));
      const previousAttackIDs = new Set(STATE.defenseHistory.map(a => a.id));

      // New attacks
      for (const attack of incomingAttacks) {
        if (!previousAttackIDs.has(attack.id())) {
          const attacker = attack.attacker();
          console.log(`[THREAT] 🚨 NEW ATTACK from ${attacker.name()} - ${attack.troops()} troops!`);

          STATE.defenseHistory.push({
            id: attack.id(),
            attacker: attacker.id(),
            attackerName: attacker.name(),
            troops: attack.troops(),
            timestamp: Date.now()
          });
        }
      }

      // Keep only recent history (last 10 attacks)
      if (STATE.defenseHistory.length > 10) {
        STATE.defenseHistory = STATE.defenseHistory.slice(-10);
      }

      return incomingAttacks.length > 0;
    } catch (error) {
      console.error("[Attack Detection] Error:", error);
      return false;
    }
  }

  /* ===== DYNAMIC OPTIMAL RATIO CALCULATION ===== */
  function calculateOptimalRatio(maxTroops) {
    // Based on game formula: growth(r) = [10 + (M×r)^0.73/4] × (1-r)
    // Optimal ratio varies with maxTroops (M)

    if (maxTroops < 100000) return 0.38;      // Early game
    if (maxTroops < 300000) return 0.40;      // Early-mid game
    if (maxTroops < 600000) return 0.42;      // Mid game
    if (maxTroops < 1000000) return 0.43;     // Late game
    return 0.44;                               // End game
  }

  /* ===== NEIGHBOR SCANNING ===== */
  async function scanNeighbors(api) {
    const { game, myPlayer } = api;

    try {
      const borderData = await myPlayer.borderTiles();
      const borderTiles = borderData.borderTiles || [];

      const neighborOwners = new Map();
      const terraNullius = game.terraNullius();

      // Scan border tiles for neighbors
      for (const borderTile of borderTiles) {
        const neighbors = game.neighbors(borderTile);
        for (const neighborTile of neighbors) {
          if (!game.isLand(neighborTile)) continue;

          const ownerID = game.ownerID(neighborTile);
          const mySmallID = myPlayer.smallID();

          if (ownerID !== mySmallID && !neighborOwners.has(ownerID)) {
            const owner = game.playerBySmallID(ownerID);
            neighborOwners.set(ownerID, owner);
          }
        }
      }

      const now = Date.now();
      const myTroops = myPlayer.troops();
      const myTiles = myPlayer.numTilesOwned();
      const myDensity = myTroops / myTiles;

      // Update neighbor tracking
      for (const [ownerID, owner] of neighborOwners) {
        if (ownerID === 0) continue; // Skip terra nullius for neighbor tracking
        if (!owner.isPlayer || !owner.isPlayer()) continue;
        if (myPlayer.isFriendly && myPlayer.isFriendly(owner)) continue;

        const neighborID = owner.id();
        const troops = owner.troops();
        const tiles = owner.numTilesOwned();
        const density = troops / tiles;

        // Get or create neighbor data
        let neighborData = STATE.neighbors.get(neighborID);
        if (!neighborData) {
          neighborData = {
            player: owner,
            troops: troops,
            tiles: tiles,
            density: density,
            lastUpdate: now,
            troopHistory: [{ time: now, troops: troops }]
          };
          STATE.neighbors.set(neighborID, neighborData);
        } else {
          // Update existing neighbor
          neighborData.troops = troops;
          neighborData.tiles = tiles;
          neighborData.density = density;
          neighborData.lastUpdate = now;
          neighborData.troopHistory.push({ time: now, troops: troops });

          // Keep only last 20 data points
          if (neighborData.troopHistory.length > 20) {
            neighborData.troopHistory = neighborData.troopHistory.slice(-20);
          }
        }
      }

      // Clean up neighbors we no longer border
      const currentNeighborIDs = new Set(Array.from(neighborOwners.values())
        .filter(o => o.isPlayer && o.isPlayer())
        .map(o => o.id()));

      for (const neighborID of STATE.neighbors.keys()) {
        if (!currentNeighborIDs.has(neighborID)) {
          STATE.neighbors.delete(neighborID);
        }
      }

      console.log(`[Neighbors] Tracking ${STATE.neighbors.size} neighbors`);

    } catch (error) {
      console.error("[Neighbors] Error scanning:", error);
    }
  }

  /* ===== THREAT DETECTION SYSTEM ===== */
  function analyzeThreat(neighborData, myTroops) {
    const { player, troops, density, troopHistory } = neighborData;

    let threatLevel = 'LOW';
    const reasons = [];

    // Check troop buildup
    if (troopHistory.length >= 2) {
      const recent = troopHistory[troopHistory.length - 1];
      const previous = troopHistory[Math.max(0, troopHistory.length - 5)];
      const troopGain = recent.troops - previous.troops;
      const timeSpan = (recent.time - previous.time) / 1000; // seconds

      if (troopGain > CONFIG.threatTroopThreshold && timeSpan > 0) {
        threatLevel = 'HIGH';
        reasons.push(`+${troopGain.toFixed(0)} troops in ${timeSpan.toFixed(0)}s`);
      }
    }

    // Check troop ratio
    const troopRatio = troops / myTroops;
    if (troopRatio > 1.5) {
      threatLevel = 'HIGH';
      reasons.push(`${(troopRatio * 100).toFixed(0)}% of your troops`);
    } else if (troopRatio > 1.0) {
      if (threatLevel === 'LOW') threatLevel = 'MEDIUM';
      reasons.push(`${(troopRatio * 100).toFixed(0)}% of your troops`);
    }

    // Check density
    const myDensity = myTroops / (neighborData.tiles || 1);
    if (density > myDensity * 1.3) {
      if (threatLevel === 'LOW') threatLevel = 'MEDIUM';
      reasons.push(`High density: ${density.toFixed(1)}`);
    }

    return {
      neighbor: player,
      threatLevel,
      reasons,
      troops,
      density,
      troopRatio
    };
  }

  function detectThreats(api) {
    const { myPlayer } = api;
    const myTroops = myPlayer.troops();

    STATE.threats = [];

    for (const neighborData of STATE.neighbors.values()) {
      const threat = analyzeThreat(neighborData, myTroops);

      if (threat.threatLevel !== 'LOW') {
        STATE.threats.push(threat);
      }
    }

    // Sort by threat level
    const threatOrder = { 'CRITICAL': 4, 'HIGH': 3, 'MEDIUM': 2, 'LOW': 1 };
    STATE.threats.sort((a, b) => threatOrder[b.threatLevel] - threatOrder[a.threatLevel]);

    if (STATE.threats.length > 0) {
      console.log(`[Threats] Detected ${STATE.threats.length} threats:`);
      for (const threat of STATE.threats.slice(0, 3)) {
        console.log(`  ${threat.threatLevel}: ${threat.neighbor.name()} - ${threat.reasons.join(', ')}`);
      }
    }
  }

  /* ===== MOMENTUM TRACKING ===== */
  function calculateMomentum(neighborData) {
    const { troopHistory } = neighborData;

    if (troopHistory.length < 3) {
      return { velocity: 0, acceleration: 0, trend: '→' };
    }

    // Calculate velocity (troops per second)
    const recent = troopHistory[troopHistory.length - 1];
    const previous = troopHistory[troopHistory.length - 3];
    const timeDiff = (recent.time - previous.time) / 1000;
    const troopDiff = recent.troops - previous.troops;
    const velocity = timeDiff > 0 ? troopDiff / timeDiff : 0;

    // Calculate acceleration
    const mid = troopHistory[troopHistory.length - 2];
    const velocity1 = (mid.troops - previous.troops) / ((mid.time - previous.time) / 1000);
    const velocity2 = (recent.troops - mid.troops) / ((recent.time - mid.time) / 1000);
    const acceleration = velocity2 - velocity1;

    // Determine trend
    let trend = '→';
    if (velocity > 50) trend = '↗️';
    else if (velocity > 100) trend = '↗️↗️';
    else if (velocity > 200) trend = '↗️↗️↗️';
    else if (velocity < -50) trend = '↘️';
    else if (velocity < -100) trend = '↘️↘️';

    return { velocity, acceleration, trend };
  }

  function updateMomentum() {
    for (const [neighborID, neighborData] of STATE.neighbors) {
      const momentum = calculateMomentum(neighborData);
      STATE.momentum.set(neighborID, momentum);
    }
  }

  /* ===== SMART ATTACK RATIO CALCULATION ===== */
  function calculateSmartAttackRatio(target, myTroops, myDensity) {
    if (!CONFIG.smartAttackRatios) {
      return null; // Use default calculation
    }

    const targetTroops = target.troops();
    const targetTiles = target.numTilesOwned();
    const targetDensity = targetTroops / targetTiles;

    // Calculate win chance
    const troopRatio = myTroops / (targetTroops + 1);
    const densityRatio = myDensity / (targetDensity + 0.1);
    const winChance = Math.min(0.95, (troopRatio + densityRatio) / 2);

    // Determine attack ratio based on win chance
    let attackRatio;

    if (winChance > 0.80) {
      // Very weak target - minimal troops needed
      attackRatio = CONFIG.weakTargetRatio;
    } else if (winChance > 0.60) {
      // Normal target
      attackRatio = CONFIG.normalTargetRatio;
    } else if (winChance > 0.40) {
      // Strong target - need more troops
      attackRatio = CONFIG.strongTargetRatio;
    } else {
      // Very strong - maximum safe attack
      attackRatio = 0.70;
    }

    // Ensure minimum
    attackRatio = Math.max(CONFIG.minAttackRatio, attackRatio);

    console.log(`[Smart Ratio] Target: ${target.name?.()} - Win: ${(winChance * 100).toFixed(0)}% - Ratio: ${(attackRatio * 100).toFixed(0)}%`);

    return attackRatio;
  }

  /* ===== BORDER PRESSURE ANALYSIS ===== */
  async function analyzeBorderPressure(api) {
    if (!CONFIG.borderAnalysisEnabled) return;

    const { game, myPlayer } = api;

    try {
      const borderData = await myPlayer.borderTiles();
      const borderTiles = borderData.borderTiles || [];

      // Count border tiles per neighbor
      const borderCounts = new Map();

      for (const borderTile of borderTiles) {
        const neighbors = game.neighbors(borderTile);
        for (const neighborTile of neighbors) {
          if (!game.isLand(neighborTile)) continue;

          const ownerID = game.ownerID(neighborTile);
          const mySmallID = myPlayer.smallID();

          if (ownerID !== mySmallID) {
            borderCounts.set(ownerID, (borderCounts.get(ownerID) || 0) + 1);
          }
        }
      }

      // Calculate pressure for each neighbor
      STATE.borderData.clear();

      for (const [ownerID, borderLength] of borderCounts) {
        if (ownerID === 0) continue; // Skip terra nullius

        const owner = game.playerBySmallID(ownerID);
        if (!owner || !owner.isPlayer || !owner.isPlayer()) continue;

        const neighborData = STATE.neighbors.get(owner.id());
        if (!neighborData) continue;

        // Pressure = (enemy density × border length) / our total border
        const pressure = (neighborData.density * borderLength) / borderTiles.size;

        // Vulnerability = pressure / our density
        const myDensity = myPlayer.troops() / myPlayer.numTilesOwned();
        const vulnerability = pressure / myDensity;

        STATE.borderData.set(owner.id(), {
          borderLength,
          pressure,
          vulnerability,
          neighbor: owner
        });
      }

      console.log(`[Border] Analyzed ${STATE.borderData.size} borders`);

    } catch (error) {
      console.error("[Border] Error analyzing:", error);
    }
  }

  /* ===== EXPANSION OPPORTUNITY DETECTION ===== */
  async function analyzeExpansionOpportunities(api) {
    if (!CONFIG.expansionAnalysisEnabled) return;

    const { game, myPlayer } = api;

    try {
      STATE.expansionTargets = [];

      // Analyze each neighbor as expansion target
      for (const neighborData of STATE.neighbors.values()) {
        const { player, density, troops } = neighborData;

        const myTroops = myPlayer.troops();
        const myDensity = myTroops / myPlayer.numTilesOwned();

        // Calculate expansion score
        const troopRatio = myTroops / (troops + 1);
        const densityAdvantage = myDensity / (density + 0.1);
        const winChance = Math.min(0.95, (troopRatio + densityAdvantage) / 2);

        // Get border data
        const borderInfo = STATE.borderData.get(player.id());
        const borderLength = borderInfo ? borderInfo.borderLength : 0;

        // Score = winChance × borderLength × (1 / density)
        // Higher score = better expansion opportunity
        const score = winChance * borderLength * (1 / (density + 1));

        let reason = '';
        if (winChance > 0.75) reason = 'Easy win';
        else if (winChance > 0.50) reason = 'Favorable';
        else if (winChance > 0.30) reason = 'Challenging';
        else reason = 'Risky';

        STATE.expansionTargets.push({
          target: player,
          score,
          winChance,
          borderLength,
          reason,
          direction: 'N/A' // Could calculate based on border tile positions
        });
      }

      // Sort by score (best opportunities first)
      STATE.expansionTargets.sort((a, b) => b.score - a.score);

      if (STATE.expansionTargets.length > 0) {
        console.log(`[Expansion] Top opportunity: ${STATE.expansionTargets[0].target.name()} (${STATE.expansionTargets[0].reason})`);
      }

    } catch (error) {
      console.error("[Expansion] Error analyzing:", error);
    }
  }

  /* ===== TARGET FINDING (Enhanced) ===== */
  async function findAllTargets(api) {
    const { game, myPlayer } = api;

    try {
      const targets = [];
      const myTroops = myPlayer.troops();
      const myTiles = myPlayer.numTilesOwned();
      const myDensity = myTroops / myTiles;

      // Add Terra Nullius if available
      const borderData = await myPlayer.borderTiles();
      const borderTiles = borderData.borderTiles || [];

      const neighborOwners = new Map();
      for (const borderTile of borderTiles) {
        const neighbors = game.neighbors(borderTile);
        for (const neighborTile of neighbors) {
          if (!game.isLand(neighborTile)) continue;
          const ownerID = game.ownerID(neighborTile);
          const mySmallID = myPlayer.smallID();
          if (ownerID !== mySmallID && !neighborOwners.has(ownerID)) {
            const owner = game.playerBySmallID(ownerID);
            neighborOwners.set(ownerID, owner);
          }
        }
      }

      if (neighborOwners.has(0)) {
        targets.push({
          targetName: "Terra Nullius",
          winChance: 1.0,
          score: 1000,
          isNeutral: true,
          recommendedRatio: CONFIG.weakTargetRatio,
          threat: 'NONE',
          momentum: '→'
        });
      }

      // Analyze all enemy neighbors
      for (const [, owner] of neighborOwners) {
        if (!owner.isPlayer || !owner.isPlayer()) continue;
        if (myPlayer.isFriendly && myPlayer.isFriendly(owner)) continue;

        const enemyTroops = owner.troops();
        const enemyTiles = owner.numTilesOwned();
        const enemyDensity = enemyTroops / enemyTiles;

        // Calculate win chance
        const troopRatio = myTroops / (enemyTroops + 1);
        const densityRatio = myDensity / (enemyDensity + 0.1);
        const winChance = Math.min(0.95, (troopRatio + densityRatio) / 2);

        // Score: prefer weak enemies with high win chance
        const score = winChance * (1 / (enemyDensity + 1));

        // Get smart attack ratio
        const recommendedRatio = calculateSmartAttackRatio(owner, myTroops, myDensity) || CONFIG.normalTargetRatio;

        // Get threat level
        const threat = STATE.threats.find(t => t.neighbor.id() === owner.id());
        const threatLevel = threat ? threat.threatLevel : 'LOW';

        // Get momentum
        const momentum = STATE.momentum.get(owner.id());
        const momentumTrend = momentum ? momentum.trend : '→';

        targets.push({
          target: owner,
          targetName: owner.name?.() || owner.displayName?.() || "Unknown",
          density: enemyDensity,
          winChance: winChance,
          score: score,
          isNeutral: false,
          recommendedRatio: recommendedRatio,
          threat: threatLevel,
          momentum: momentumTrend
        });
      }

      // Sort by score (easiest to attack first)
      targets.sort((a, b) => b.score - a.score);

      return targets;

    } catch (error) {
      console.error("[Tactical] Error finding targets:", error);
      return [];
    }
  }

  /* ===== GROWTH MATH WITH RISK MANAGEMENT ===== */
  function calculateOptimalSlider(api) {
    const { game, myPlayer } = api;

    try {
      const troops = myPlayer.troops();
      const maxTroops = game.config().maxTroops(myPlayer);
      const currentRatio = troops / maxTroops;

      // Calculate dynamic optimal ratio based on game phase
      const optimalRatio = calculateOptimalRatio(maxTroops);
      STATE.currentOptimalRatio = optimalRatio;

      // Check if under attack
      const underAttack = detectAttackChanges(api);

      let targetSlider = 0;
      let reason = "";
      let status = "OPTIMAL";
      let riskLevel = "LOW";

      // Risk management: if under attack, keep higher reserve
      if (underAttack) {
        const safeRatio = currentRatio - CONFIG.underAttackReserve;
        if (safeRatio > 0) {
          targetSlider = Math.max(CONFIG.minAttackRatio * 100, (safeRatio / currentRatio) * 100);
          reason = `⚠️ UNDER ATTACK - Reserved ${(CONFIG.underAttackReserve * 100).toFixed(0)}%`;
          status = "DEFENSIVE";
          riskLevel = "HIGH";
        } else {
          targetSlider = 0;
          reason = `🛡️ DEFENDING - Building reserves`;
          status = "DEFENSIVE";
          riskLevel = "CRITICAL";
        }
      } else if (currentRatio > optimalRatio + CONFIG.tolerance) {
        // Too many troops - attack to reduce to optimal
        const targetTroops = optimalRatio * maxTroops;
        const excessTroops = troops - targetTroops;

        // Calculate as percentage of CURRENT troops (what the slider uses)
        let rawSlider = (excessTroops / troops) * 100;

        // When WAY over optimal, be more aggressive
        const overage = currentRatio - optimalRatio;
        if (overage > CONFIG.aggressiveThreshold) {
          const boostFactor = 1 + Math.min(0.3, overage * 0.5);
          rawSlider = rawSlider * boostFactor;
        }

        // NEVER go below minimum attack ratio, cap at 80% for safety
        targetSlider = Math.max(CONFIG.minAttackRatio * 100, Math.min(80, rawSlider));

        reason = `Excess: ${(currentRatio * 100).toFixed(1)}% > ${(optimalRatio * 100).toFixed(1)}%`;
        status = "EXCESS";
        riskLevel = "LOW";
      } else if (currentRatio < optimalRatio - CONFIG.tolerance) {
        // Too few troops - don't attack, let grow
        targetSlider = 0;
        reason = `Growing: ${(currentRatio * 100).toFixed(1)}% < ${(optimalRatio * 100).toFixed(1)}%`;
        status = "GROWING";
        riskLevel = "MEDIUM";
      } else {
        // In optimal range
        targetSlider = 0;
        reason = `Optimal: ${(currentRatio * 100).toFixed(1)}% ≈ ${(optimalRatio * 100).toFixed(1)}%`;
        status = "OPTIMAL";
        riskLevel = "LOW";
      }

      // Final safety check
      let finalSlider = Math.round(targetSlider);
      if (finalSlider > 0 && finalSlider < CONFIG.minAttackRatio * 100) {
        console.warn(`[Safety] Slider was ${finalSlider}%, forcing to minimum ${CONFIG.minAttackRatio * 100}%`);
        finalSlider = Math.round(CONFIG.minAttackRatio * 100);
      }

      return {
        currentRatio,
        optimalRatio,
        targetSlider: finalSlider,
        reason,
        status,
        riskLevel,
        underAttack,
        troops,
        maxTroops
      };
    } catch (error) {
      console.error("[Math] Error calculating optimal slider:", error);
      return null;
    }
  }

  /* ===== MAIN LOOP ===== */
  let currentTarget = [];
  const TARGET_UPDATE_INTERVAL = 3000;
  let lastTargetUpdate = 0;

  async function mainLoop() {
    const api = getGameAPI();
    if (!api) {
      console.log("[Main Loop] Waiting for game API...");
      return;
    }

    const now = Date.now();

    // Update neighbors periodically
    if (now - STATE.lastNeighborUpdate > TARGET_UPDATE_INTERVAL) {
      await scanNeighbors(api);
      STATE.lastNeighborUpdate = now;
    }

    // Update threats
    if (now - STATE.lastThreatUpdate > CONFIG.threatUpdateInterval) {
      detectThreats(api);
      updateMomentum();
      STATE.lastThreatUpdate = now;
    }

    // Update border analysis
    if (now - STATE.lastBorderUpdate > CONFIG.borderUpdateInterval) {
      await analyzeBorderPressure(api);
      STATE.lastBorderUpdate = now;
    }

    // Update expansion analysis
    if (now - STATE.lastExpansionUpdate > CONFIG.expansionUpdateInterval) {
      await analyzeExpansionOpportunities(api);
      STATE.lastExpansionUpdate = now;
    }

    // Update target list
    if (now - lastTargetUpdate > TARGET_UPDATE_INTERVAL) {
      currentTarget = await findAllTargets(api);
      lastTargetUpdate = now;
      console.log(`[Tactical] Found ${currentTarget.length} targets`);
    }

    // Always update UI
    const calc = calculateOptimalSlider(api);
    if (calc) {
      updateMetrics(calc, currentTarget);
    } else {
      console.error("[Main Loop] calculateOptimalSlider returned null!");
    }

    // STOP HERE if not enabled - don't touch slider!
    if (!CONFIG.enabled) return;

    // Clear first run flag once enabled
    if (STATE.isFirstRun) {
      STATE.isFirstRun = false;
      console.log("[Auto] Manager enabled - taking control of slider");
    }

    // NOW it's safe to manipulate the slider
    if (CONFIG.mode === 'auto') {
      // Use uiState like v7 for a reliable reading
      const currentSlider = Math.round((api.uiState?.attackRatio || 0) * 100);

      // Assisted floor: keep a practical minimum so clicks do something in early game
      const floor = Math.round(Math.max(CONFIG.minAttackRatio * 100, CONFIG.assistedFloorRatio * 100));

      // If our calculation decides "0" (growth), honor a floor to avoid 1%/0% trap
      const desired = calc.targetSlider === 0 ? floor : calc.targetSlider;

      if (Math.abs(currentSlider - desired) > 2) {
        setSliderValue(desired);
      }
    } else if (CONFIG.mode === 'manual') {
      setSliderValue(CONFIG.manualSliderValue);
    }
  }

  /* ===== UI CREATION ===== */
  function createUI() {
    const widget = document.createElement('div');
    widget.id = 'auto-script-v1-widget';
    widget.innerHTML = `
      <div style="
        position: fixed;
        top: ${CONFIG.position.y}px;
        left: ${CONFIG.position.x}px;
        background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
        border: 2px solid #3b82f6;
        border-radius: 12px;
        padding: 16px;
        color: #e2e8f0;
        font-family: 'Segoe UI', system-ui, sans-serif;
        font-size: 13px;
        z-index: 10000;
        min-width: 320px;
        box-shadow: 0 10px 40px rgba(0,0,0,0.5), 0 0 20px rgba(59,130,246,0.3);
        cursor: move;
        user-select: none;
      " class="auto-script-widget">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; border-bottom: 1px solid #334155; padding-bottom: 8px;">
          <span style="font-weight: 700; font-size: 15px; color: #60a5fa;">
            🎯 Auto Script V1 (Advanced Tactical Assistant)
          </span>
          <button id="collapse-btn" style="background: #334155; border: none; color: #94a3b8; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 11px;">−</button>
        </div>

        <div id="widget-content" style="${CONFIG.collapsed ? 'display: none;' : ''}">
          <!-- Controls -->
          <div style="margin-bottom: 12px; padding: 10px; background: #0f172a; border-radius: 6px;">
            <label style="display: flex; align-items: center; margin-bottom: 8px; cursor: pointer;">
              <input type="checkbox" id="enable-manager" ${CONFIG.enabled ? 'checked' : ''} style="margin-right: 8px; cursor: pointer;">
              <span style="font-weight: 600;">Enable Manager</span>
            </label>
            <div style="display: flex; gap: 8px;">
              <label style="flex: 1; display: flex; align-items: center; cursor: pointer;">
                <input type="radio" name="mode" value="auto" ${CONFIG.mode === 'auto' ? 'checked' : ''} style="margin-right: 6px; cursor: pointer;">
                <span>Auto</span>
              </label>
              <label style="flex: 1; display: flex; align-items: center; cursor: pointer;">
                <input type="radio" name="mode" value="manual" ${CONFIG.mode === 'manual' ? 'checked' : ''} style="margin-right: 6px; cursor: pointer;">
                <span>Manual</span>
              </label>
            </div>
          </div>

          <!-- Metrics -->
          <div style="background: #0f172a; border-radius: 6px; padding: 10px; margin-bottom: 12px;">
            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
              <span style="color: #94a3b8;">Current Ratio:</span>
              <span id="current-ratio" style="font-weight: 700; color: #3b82f6;">--</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
              <span style="color: #94a3b8;">Optimal Ratio:</span>
              <span id="optimal-ratio" style="font-weight: 700; color: #10b981;">--</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
              <span style="color: #94a3b8;">Attack Slider:</span>
              <span id="slider-value" style="font-weight: 700; color: #f59e0b;">--</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
              <span style="color: #94a3b8;">Risk Level:</span>
              <span id="risk-level" style="font-weight: 700;">--</span>
            </div>
            <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
              <span style="color: #94a3b8;">Threats:</span>
              <span id="threat-count" style="font-weight: 700; color: #ef4444;">--</span>
            </div>
          </div>

          <!-- Status -->
          <div style="background: #0f172a; border-radius: 6px; padding: 8px; margin-bottom: 12px;">
            <div id="status-text" style="font-size: 12px; color: #cbd5e1; text-align: center;">Initializing...</div>
          </div>

          <!-- Target List -->
          <div style="background: #0f172a; border-radius: 6px; padding: 10px; max-height: 300px; overflow-y: auto;">
            <div style="font-weight: 600; margin-bottom: 8px; color: #60a5fa; font-size: 13px;">🎯 Attack Targets:</div>
            <div id="target-list" style="font-size: 12px;">
              <div style="color: #64748b; text-align: center; padding: 20px;">Scanning for neighbors...</div>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(widget);

    // Make draggable
    const widgetEl = widget.querySelector('.auto-script-widget');
    let isDragging = false;
    let currentX;
    let currentY;
    let initialX;
    let initialY;

    widgetEl.addEventListener('mousedown', (e) => {
      if (e.target.tagName === 'INPUT' || e.target.tagName === 'BUTTON') return;
      isDragging = true;
      initialX = e.clientX - CONFIG.position.x;
      initialY = e.clientY - CONFIG.position.y;
    });

    document.addEventListener('mousemove', (e) => {
      if (isDragging) {
        e.preventDefault();
        currentX = e.clientX - initialX;
        currentY = e.clientY - initialY;
        CONFIG.position.x = currentX;
        CONFIG.position.y = currentY;
        widgetEl.style.left = currentX + 'px';
        widgetEl.style.top = currentY + 'px';
      }
    });

    document.addEventListener('mouseup', () => {
      if (isDragging) {
        isDragging = false;
        saveConfig();
      }
    });

    // Event listeners
    document.getElementById('enable-manager').addEventListener('change', (e) => {
      CONFIG.enabled = e.target.checked;
      saveConfig();
      console.log(`[Config] Manager ${CONFIG.enabled ? 'enabled' : 'disabled'}`);
    });

    document.querySelectorAll('input[name="mode"]').forEach(radio => {
      radio.addEventListener('change', (e) => {
        CONFIG.mode = e.target.value;
        saveConfig();
        console.log(`[Config] Mode changed to: ${CONFIG.mode}`);
      });
    });

    document.getElementById('collapse-btn').addEventListener('click', () => {
      CONFIG.collapsed = !CONFIG.collapsed;
      const content = document.getElementById('widget-content');
      const btn = document.getElementById('collapse-btn');
      content.style.display = CONFIG.collapsed ? 'none' : 'block';
      btn.textContent = CONFIG.collapsed ? '+' : '−';
      saveConfig();
    });
  }

  /* ===== UPDATE METRICS ===== */
  function updateMetrics(calc, targets) {
    if (!calc) return;

    const currentRatioEl = document.getElementById('current-ratio');
    const optimalRatioEl = document.getElementById('optimal-ratio');
    const sliderValueEl = document.getElementById('slider-value');
    const statusTextEl = document.getElementById('status-text');
    const riskLevelEl = document.getElementById('risk-level');
    const threatCountEl = document.getElementById('threat-count');
    const targetListEl = document.getElementById('target-list');

    if (currentRatioEl) {
      currentRatioEl.textContent = `${(calc.currentRatio * 100).toFixed(1)}%`;

      // Color based on how close to optimal
      const diff = Math.abs(calc.currentRatio - calc.optimalRatio);
      if (diff < CONFIG.tolerance) {
        currentRatioEl.style.color = '#10b981'; // Green
      } else if (diff < CONFIG.tolerance * 2) {
        currentRatioEl.style.color = '#f59e0b'; // Orange
      } else {
        currentRatioEl.style.color = '#ef4444'; // Red
      }
    }

    // Update optimal ratio display (dynamic based on maxTroops)
    if (optimalRatioEl) {
      optimalRatioEl.textContent = `${(calc.optimalRatio * 100).toFixed(1)}%`;
    }

    if (sliderValueEl) {
      const minRatio = CONFIG.minAttackRatio * 100;
      sliderValueEl.textContent = `${calc.targetSlider}% ${calc.targetSlider > 0 && calc.targetSlider < minRatio + 5 ? '(min)' : ''}`;
    }

    if (riskLevelEl) {
      riskLevelEl.textContent = calc.riskLevel;

      // Color based on risk
      if (calc.riskLevel === 'LOW') {
        riskLevelEl.style.color = '#10b981';
      } else if (calc.riskLevel === 'MEDIUM') {
        riskLevelEl.style.color = '#f59e0b';
      } else if (calc.riskLevel === 'HIGH') {
        riskLevelEl.style.color = '#ef4444';
      } else {
        riskLevelEl.style.color = '#dc2626';
      }
    }

    if (threatCountEl) {
      const threatCount = STATE.threats.length;
      threatCountEl.textContent = threatCount > 0 ? `${threatCount} detected` : 'None';
      threatCountEl.style.color = threatCount > 0 ? '#ef4444' : '#10b981';
    }

    if (statusTextEl) {
      statusTextEl.textContent = calc.reason;
    }

    // Update target list
    if (targetListEl && Array.isArray(targets)) {
      if (targets.length === 0) {
        targetListEl.innerHTML = '<div style="color: #64748b; text-align: center; padding: 20px;">No targets found</div>';
      } else {
        let html = '';
        for (const target of targets.slice(0, 10)) { // Show top 10
          const winColor = target.winChance > 0.75 ? '#10b981' : target.winChance > 0.50 ? '#f59e0b' : '#ef4444';
          const threatColor = target.threat === 'HIGH' ? '#ef4444' : target.threat === 'MEDIUM' ? '#f59e0b' : '#64748b';

          html += `
            <div style="
              padding: 8px;
              margin-bottom: 6px;
              background: #1e293b;
              border-radius: 4px;
              border-left: 3px solid ${winColor};
            ">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
                <span style="font-weight: 600; color: #e2e8f0;">${target.targetName}</span>
                <span style="color: ${winColor}; font-size: 11px;">${(target.winChance * 100).toFixed(0)}% win</span>
              </div>
              <div style="display: flex; justify-content: space-between; font-size: 11px; color: #94a3b8;">
                <span>Ratio: ${(target.recommendedRatio * 100).toFixed(0)}%</span>
                <span style="color: ${threatColor};">Threat: ${target.threat}</span>
                <span>${target.momentum}</span>
              </div>
            </div>
          `;
        }
        targetListEl.innerHTML = html;
      }
    }
  }

  /* ===== INITIALIZATION ===== */
  function init() {
    console.log("[Init] Waiting for game to load...");

    // Wait for control panel (same as v7)
    const checkInterval = setInterval(() => {
      const controlPanel = document.querySelector('control-panel');
      if (controlPanel) {
        console.log("[Init] ✅ Control panel found!");
        clearInterval(checkInterval);

        // Create UI
        createUI();
        console.log("[Init] ✅ UI created");

        // Start main loop
        setInterval(mainLoop, 1000); // Run every second
        console.log("[Init] ✅ Main loop started");

        console.log("╔════════════════════════════════════════════════════════════╗");
        console.log("║  ✅ Auto Script V1 initialized successfully!              ║");
        console.log("║  📊 All systems operational                                ║");
        console.log("╚════════════════════════════════════════════════════════════╝");
      }
    }, 1000);
  }

  // Start when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

})();

