name: 🧪 CI

on:
  merge_group:
    types:
      - checks_requested
  pull_request:
  push:
    branches: [main]

permissions: {}

jobs:
  build:
    name: 🏗️ Build
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: false
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - run: npm ci
      - run: npm run build-prod
      - uses: actions/upload-artifact@v4
        with:
          path: out/index.html
          retention-days: 1
  test:
    name: 🔬 Test
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          submodules: false
      - name: Setup node
        uses: actions/setup-node@v4
        with:
          node-version: 20
      - run: npm ci
      - run: npm run test:coverage

  eslint:
    name: 🔍 ESLint
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
      - run: npm ci
      - run: npx eslint --format gha

  prettier:
    name: 🎨 Prettier
    runs-on: ubuntu-latest
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"
      - run: npm ci
      - run: npx prettier --check .
