// ==UserScript==
// @name         OpenFrontIO Smart Autopilot v7 (Tactical)
// @namespace    https://openfront.io/
// @version      8.0.0
// @description  Tactical autopilot with risk management, min attack ratio, and target analysis
// @match        https://openfront.io/*
// @match        https://*.openfront.io/*
// @match        http://localhost/*
// @match        http://127.0.0.1/*
// @grant        none
// @run-at       document-idle
// ==/UserScript==

(function () {
  "use strict";

  console.log("╔════════════════════════════════════════════════════════════╗");
  console.log("║     ⚔️  Smart Autopilot v8 (Tactical)                    ║");
  console.log("║     • Risk Management & Min Attack Ratio                  ║");
  console.log("║     • Target Analysis & Win Chance Indicators             ║");
  console.log("╚════════════════════════════════════════════════════════════╝");

  /* ===== CONFIGURATION ===== */
  const CONFIG = {
    enabled: false,
    mode: 'auto', // 'auto' or 'manual'

    // Growth math constants (DYNAMIC - calculated based on maxTroops)
    baseOptimalRatio: 0.42,    // Base optimal ratio for reference
    tolerance: 0.03,           // ±3% tolerance before adjusting (tighter for faster response)
    aggressiveThreshold: 0.15, // If more than 15% over optimal, be aggressive

    // Risk management
    minAttackRatio: 0.15,      // Never attack with less than 15% (safety buffer)
    underAttackReserve: 0.30,  // Keep 30% reserve when under attack

    // Manual mode
    manualSliderValue: 50,     // Manual slider percentage

    // UI state
    collapsed: false,
    position: { x: 20, y: 20 }
  };

  let gameAPI = null;
  let lastLogTime = 0;
  let currentTarget = null;
  let lastTargetUpdate = 0;
  let isFirstRun = true; // Track first run to avoid setting slider before enabled
  const LOG_INTERVAL = 5000; // Log every 5 seconds
  const TARGET_UPDATE_INTERVAL = 3000; // Update target every 3 seconds

  /* ===== GAME API ACCESS ===== */
  function getGameAPI() {
    if (gameAPI && gameAPI.isValid()) {
      return gameAPI;
    }

    const controlPanel = document.querySelector('control-panel');
    if (!controlPanel) return null;

    const game = controlPanel.game;
    const uiState = controlPanel.uiState;

    if (!game || !uiState) return null;

    const myPlayer = game.myPlayer();
    if (!myPlayer || !myPlayer.isAlive()) return null;

    gameAPI = {
      game,
      uiState,
      myPlayer,
      isValid: () => {
        const player = game.myPlayer();
        return player !== null && player.isAlive();
      }
    };

    console.log("[API] ✅ Game API initialized!");
    console.log("[API] Player:", myPlayer.name?.() || myPlayer.displayName?.() || "Unknown");

    return gameAPI;
  }

  /* ===== SLIDER CONTROL ===== */
  function setSliderValue(percentage) {
    const slider = document.querySelector('#attack-ratio');
    if (!slider) return false;

    try {
      // CRITICAL SAFETY: If percentage > 0, enforce minimum attack ratio
      let safePercentage = percentage;
      if (safePercentage > 0 && safePercentage < CONFIG.minAttackRatio * 100) {
        console.warn(`[SAFETY] Blocked ${safePercentage.toFixed(1)}% attack - enforcing minimum ${CONFIG.minAttackRatio * 100}%`);
        safePercentage = CONFIG.minAttackRatio * 100;
      }

      // Calculate target value based on slider's max attribute
      const max = Number(slider.max || 100);
      const currentValue = Number(slider.value || 0);
      const targetValue = Math.round(Math.max(0, Math.min(max, safePercentage)));

      // Only update if value actually changes
      if (currentValue === targetValue) return true;

      // Batch DOM write using requestAnimationFrame
      requestAnimationFrame(() => {
        slider.value = String(targetValue);

        // Dispatch BOTH input and change events (critical for game to recognize change)
        slider.dispatchEvent(new Event('input', { bubbles: true }));
        slider.dispatchEvent(new Event('change', { bubbles: true }));

        // Fallback: manually update visual fill track if app doesn't redraw
        const fillTrack = slider.closest('.relative')?.querySelector('.bg-red-500\\/60');
        if (fillTrack) {
          fillTrack.style.width = `${Math.max(0, Math.min(100, (targetValue / max) * 100))}%`;
        }
      });

      return true;
    } catch (error) {
      console.error("[Slider] Error setting value:", error);
      return false;
    }
  }

  /* ===== TACTICAL ANALYSIS ===== */
  function isUnderAttack(api) {
    try {
      const { myPlayer } = api;
      const incomingAttacks = myPlayer.incomingAttacks?.() || [];
      return incomingAttacks.length > 0;
    } catch (error) {
      return false;
    }
  }

  async function findAllTargets(api) {
    const { game, myPlayer } = api;

    try {
      // Get terra nullius (neutral land) - smallID 0
      const terraNullius = game.playerBySmallID(0);

      // Get my border tiles to find neighbors (ALWAYS refresh this!)
      const borderData = await myPlayer.borderTiles();
      const borderTiles = borderData.borderTiles || [];

      // Find all neighboring owners
      const neighborOwners = new Map(); // Map of smallID -> owner

      for (const borderTile of borderTiles) {
        const neighbors = game.neighbors(borderTile);
        for (const neighborTile of neighbors) {
          if (!game.isLand(neighborTile)) continue;

          const ownerID = game.ownerID(neighborTile);
          const mySmallID = myPlayer.smallID();

          if (ownerID !== mySmallID && !neighborOwners.has(ownerID)) {
            const owner = game.playerBySmallID(ownerID);
            neighborOwners.set(ownerID, owner);
          }
        }
      }

      const targets = [];
      const myTroops = myPlayer.troops();
      const myTiles = myPlayer.numTilesOwned();
      const myDensity = myTroops / myTiles;

      // Add terra nullius if available (always easiest)
      if (neighborOwners.has(0)) {
        targets.push({
          target: terraNullius,
          targetName: "Terra Nullius",
          density: 0,
          winChance: 1.0,
          score: 1000, // Highest score
          isNeutral: true
        });
      }

      // Analyze all enemy neighbors
      for (const [, owner] of neighborOwners) {
        if (!owner.isPlayer || !owner.isPlayer()) continue;
        if (myPlayer.isFriendly && myPlayer.isFriendly(owner)) continue;

        const enemyTroops = owner.troops();
        const enemyTiles = owner.numTilesOwned();
        const enemyDensity = enemyTroops / enemyTiles;

        // Calculate win chance (simplified)
        const troopRatio = myTroops / (enemyTroops + 1);
        const densityRatio = myDensity / (enemyDensity + 0.1);
        const winChance = Math.min(0.95, (troopRatio + densityRatio) / 2);

        // Score: prefer weak enemies with high win chance
        const score = winChance * (1 / (enemyDensity + 1));

        targets.push({
          target: owner,
          targetName: owner.name?.() || owner.displayName?.() || "Unknown",
          density: enemyDensity,
          winChance: winChance,
          score: score,
          isNeutral: false
        });
      }

      // Sort by score (easiest to attack first)
      targets.sort((a, b) => b.score - a.score);

      return targets;

    } catch (error) {
      console.error("[Tactical] Error finding targets:", error);
      return [];
    }
  }

  /* ===== DYNAMIC OPTIMAL RATIO CALCULATION ===== */
  function calculateOptimalRatio(maxTroops) {
    // Based on game formula: growth(r) = [10 + (M×r)^0.73/4] × (1-r)
    // Optimal ratio varies with maxTroops (M)
    // Derived from numerical analysis of the growth function

    if (maxTroops < 100000) return 0.38;      // Early game: lower ratio for expansion
    if (maxTroops < 300000) return 0.40;      // Early-mid game
    if (maxTroops < 600000) return 0.42;      // Mid game
    if (maxTroops < 1000000) return 0.43;     // Late game
    return 0.44;                               // End game: higher ratio for efficiency
  }

  /* ===== GROWTH MATH WITH RISK MANAGEMENT ===== */
  function calculateOptimalSlider(api) {
    const { game, myPlayer } = api;

    try {
      const troops = myPlayer.troops();
      const maxTroops = game.config().maxTroops(myPlayer);
      const currentRatio = troops / maxTroops;

      // Calculate dynamic optimal ratio based on game phase
      const optimalRatio = calculateOptimalRatio(maxTroops);

      // Check if under attack
      const underAttack = isUnderAttack(api);

      let targetSlider = 0;
      let reason = "";
      let status = "OPTIMAL";
      let riskLevel = "LOW";

      // Risk management: if under attack, keep higher reserve
      if (underAttack) {
        const safeRatio = currentRatio - CONFIG.underAttackReserve;
        if (safeRatio > 0) {
          targetSlider = Math.max(CONFIG.minAttackRatio * 100, (safeRatio / currentRatio) * 100);
          reason = `⚠️ UNDER ATTACK - Reserved ${(CONFIG.underAttackReserve * 100).toFixed(0)}%`;
          status = "DEFENSIVE";
          riskLevel = "HIGH";
        } else {
          targetSlider = 0;
          reason = `🛡️ DEFENDING - Building reserves`;
          status = "DEFENSIVE";
          riskLevel = "CRITICAL";
        }
      } else if (currentRatio > optimalRatio + CONFIG.tolerance) {
        // Too many troops - attack to reduce to optimal
        const targetTroops = optimalRatio * maxTroops;
        const excessTroops = troops - targetTroops;

        // Calculate as percentage of CURRENT troops (what the slider uses)
        // The game uses: attackTroops = currentTroops × sliderValue
        let rawSlider = (excessTroops / troops) * 100;

        // When WAY over optimal, be more aggressive to get back faster
        const overage = currentRatio - optimalRatio;
        if (overage > CONFIG.aggressiveThreshold) {
          // Boost slider by up to 30% when severely over optimal
          const boostFactor = 1 + Math.min(0.3, overage * 0.5);
          rawSlider = rawSlider * boostFactor;
        }

        // NEVER go below minimum attack ratio, cap at 80% for safety
        targetSlider = Math.max(CONFIG.minAttackRatio * 100, Math.min(80, rawSlider));

        reason = `Excess: ${(currentRatio * 100).toFixed(1)}% > ${(optimalRatio * 100).toFixed(1)}%`;
        status = "EXCESS";
        riskLevel = "LOW";
      } else if (currentRatio < optimalRatio - CONFIG.tolerance) {
        // Too few troops - don't attack, let grow
        targetSlider = 0;
        reason = `Growing: ${(currentRatio * 100).toFixed(1)}% < ${(optimalRatio * 100).toFixed(1)}%`;
        status = "GROWING";
        riskLevel = "MEDIUM";
      } else {
        // In optimal range
        targetSlider = 0;
        reason = `Optimal: ${(currentRatio * 100).toFixed(1)}% ≈ ${(optimalRatio * 100).toFixed(1)}%`;
        status = "OPTIMAL";
        riskLevel = "LOW";
      }

      // Final safety check: if slider > 0, ensure it's at least minAttackRatio
      let finalSlider = Math.round(targetSlider);
      if (finalSlider > 0 && finalSlider < CONFIG.minAttackRatio * 100) {
        console.warn(`[Safety] Slider was ${finalSlider}%, forcing to minimum ${CONFIG.minAttackRatio * 100}%`);
        finalSlider = Math.round(CONFIG.minAttackRatio * 100);
      }

      return {
        currentRatio,
        optimalRatio,        // Include dynamic optimal ratio
        targetSlider: finalSlider,
        reason,
        status,
        riskLevel,
        underAttack,
        troops,
        maxTroops
      };
    } catch (error) {
      console.error("[Math] Error calculating optimal slider:", error);
      return null;
    }
  }

  /* ===== MAIN LOOP ===== */
  async function mainLoop() {
    const api = getGameAPI();
    if (!api) return;

    const now = performance.now();

    // ALWAYS update targets list (neighbors change as territories expand!)
    if (now - lastTargetUpdate > TARGET_UPDATE_INTERVAL) {
      currentTarget = await findAllTargets(api);
      lastTargetUpdate = now;
      console.log(`[Tactical] Found ${currentTarget.length} neighbors`);
    }

    // Always update UI with current targets (even when disabled)
    const calc = calculateOptimalSlider(api);
    if (calc) {
      updateMetrics(calc, currentTarget);
    }

    // STOP HERE if not enabled - don't touch the slider!
    if (!CONFIG.enabled) return;

    // Clear first run flag once enabled
    if (isFirstRun) {
      isFirstRun = false;
      console.log("[Auto] Manager enabled - taking control of slider");
    }

    if (CONFIG.mode === 'auto') {
      // Auto mode: calculate and set optimal slider
      if (!calc) return;

      const currentSlider = Math.round(api.uiState.attackRatio * 100);

      // Only adjust if difference is significant
      if (Math.abs(currentSlider - calc.targetSlider) > 2) {
        setSliderValue(calc.targetSlider);

        // Log periodically
        if (now - lastLogTime > LOG_INTERVAL) {
          console.log(`[Auto] ${calc.status}: ${currentSlider}% → ${calc.targetSlider}%`);
          console.log(`[Auto] ${calc.reason}`);
          console.log(`[Auto] Risk: ${calc.riskLevel} | Under Attack: ${calc.underAttack}`);
          console.log(`[Auto] Troops: ${calc.troops.toFixed(0)}/${calc.maxTroops.toFixed(0)} (${(calc.currentRatio * 100).toFixed(1)}%)`);
          if (currentTarget && currentTarget.length > 0) {
            console.log(`[Auto] Best Target: ${currentTarget[0].targetName} (Win: ${(currentTarget[0].winChance * 100).toFixed(0)}%)`);
          }
          lastLogTime = now;
        }
      }

    } else {
      // Manual mode: set to manual value
      const currentSlider = Math.round(api.uiState.attackRatio * 100);
      if (Math.abs(currentSlider - CONFIG.manualSliderValue) > 2) {
        setSliderValue(CONFIG.manualSliderValue);
        console.log(`[Manual] Setting slider to ${CONFIG.manualSliderValue}%`);
      }
    }
  }

  /* ===== UI ===== */
  function createUI() {
    const container = document.createElement('div');
    container.id = 'slider-manager-ui';
    container.style.cssText = `
      position: fixed;
      top: ${CONFIG.position.y}px;
      right: ${CONFIG.position.x}px;
      z-index: 999999;
      background: linear-gradient(135deg, rgba(15,23,42,0.98), rgba(30,41,59,0.98));
      color: #f1f5f9;
      border: 2px solid rgba(59,130,246,0.5);
      border-radius: 12px;
      font-family: 'Consolas', 'Monaco', monospace;
      font-size: 12px;
      min-width: 320px;
      box-shadow: 0 20px 25px -5px rgba(0,0,0,0.5), 0 0 15px rgba(59,130,246,0.3);
      backdrop-filter: blur(10px);
    `;

    container.innerHTML = `
      <div id="ui-header" style="
        padding: 12px 16px;
        background: rgba(59,130,246,0.1);
        border-bottom: 1px solid rgba(59,130,246,0.3);
        cursor: move;
        display: flex;
        justify-content: space-between;
        align-items: center;
        user-select: none;
      ">
        <div style="font-weight: 700; color: #3b82f6; font-size: 13px;">
          ⚔️ Smart Autopilot v8 (Tactical)
        </div>
        <button id="collapse-btn" style="
          background: none;
          border: none;
          color: #94a3b8;
          cursor: pointer;
          font-size: 16px;
          padding: 0;
          width: 20px;
          height: 20px;
        ">▼</button>
      </div>
      
      <div id="ui-content" style="padding: 16px;">
        <!-- Tactical Targets List -->
        <div id="tactical-indicator" style="
          padding: 10px;
          background: rgba(16,185,129,0.1);
          border: 1px solid rgba(16,185,129,0.3);
          border-radius: 6px;
          margin-bottom: 12px;
          font-size: 10px;
          max-height: 200px;
          overflow-y: auto;
        ">
          <div style="font-weight: 700; color: #10b981; margin-bottom: 6px; font-size: 11px;">🎯 Neighbors (Easiest → Hardest)</div>
          <div id="target-list" style="color: #94a3b8;">Scanning...</div>
        </div>

        <!-- Enable Toggle -->
        <label style="display: flex; align-items: center; gap: 8px; margin-bottom: 12px; cursor: pointer;">
          <input type="checkbox" id="enable-toggle" ${CONFIG.enabled ? 'checked' : ''}>
          <span style="font-weight: 600;">Enable Manager</span>
        </label>

        <!-- Mode Selection -->
        <div style="margin-bottom: 12px;">
          <label style="display: block; margin-bottom: 4px; font-size: 10px; color: #94a3b8; text-transform: uppercase;">Mode:</label>
          <select id="mode-select" style="
            width: 100%;
            padding: 6px 8px;
            border-radius: 6px;
            background: rgba(0,0,0,0.3);
            color: white;
            border: 1px solid rgba(148,163,184,0.3);
            font-family: inherit;
          ">
            <option value="auto" ${CONFIG.mode === 'auto' ? 'selected' : ''}>🤖 Auto (Optimal Growth)</option>
            <option value="manual" ${CONFIG.mode === 'manual' ? 'selected' : ''}>🎮 Manual Control</option>
          </select>
        </div>

        <!-- Manual Slider (hidden in auto mode) -->
        <div id="manual-controls" style="margin-bottom: 12px; display: ${CONFIG.mode === 'manual' ? 'block' : 'none'};">
          <label style="display: block; margin-bottom: 4px; font-size: 10px; color: #94a3b8; text-transform: uppercase;">
            Manual Slider: <span id="manual-value">${CONFIG.manualSliderValue}%</span>
          </label>
          <input type="range" id="manual-slider" min="0" max="100" value="${CONFIG.manualSliderValue}" style="width: 100%;">
        </div>

        <!-- Metrics Display -->
        <div id="metrics" style="
          background: rgba(0,0,0,0.3);
          border-radius: 6px;
          padding: 10px;
          font-size: 11px;
          line-height: 1.6;
        ">
          <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
            <span style="color: #94a3b8;">Current Ratio:</span>
            <span id="current-ratio" style="font-weight: 700; color: #3b82f6;">--</span>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
            <span style="color: #94a3b8;">Optimal Ratio:</span>
            <span id="optimal-ratio" style="font-weight: 700; color: #10b981;">--</span>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
            <span style="color: #94a3b8;">Attack Slider:</span>
            <span id="slider-value" style="font-weight: 700; color: #f59e0b;">--</span>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
            <span style="color: #94a3b8;">Risk Level:</span>
            <span id="risk-level" style="font-weight: 700;">--</span>
          </div>
          <div style="display: flex; justify-content: space-between;">
            <span style="color: #94a3b8;">Status:</span>
            <span id="status-text" style="font-weight: 700;">--</span>
          </div>
        </div>

        <!-- Log -->
        <div id="log-container" style="
          margin-top: 12px;
          padding: 8px;
          background: rgba(0,0,0,0.4);
          border-radius: 6px;
          font-size: 10px;
          color: #64748b;
          max-height: 100px;
          overflow-y: auto;
        ">
          <div id="log-content">Waiting for game...</div>
        </div>
      </div>
    `;

    document.body.appendChild(container);

    // Make draggable
    makeDraggable(container);

    // Event listeners
    document.getElementById('enable-toggle').addEventListener('change', (e) => {
      CONFIG.enabled = e.target.checked;
      console.log(`[UI] Manager ${CONFIG.enabled ? 'ENABLED' : 'DISABLED'}`);
      if (CONFIG.enabled && !sliderTestPassed) {
        testSlider();
      }
    });

    document.getElementById('mode-select').addEventListener('change', (e) => {
      CONFIG.mode = e.target.value;
      document.getElementById('manual-controls').style.display = CONFIG.mode === 'manual' ? 'block' : 'none';
      console.log(`[UI] Mode: ${CONFIG.mode}`);
    });

    document.getElementById('manual-slider').addEventListener('input', (e) => {
      CONFIG.manualSliderValue = parseInt(e.target.value);
      document.getElementById('manual-value').textContent = `${CONFIG.manualSliderValue}%`;
    });

    document.getElementById('collapse-btn').addEventListener('click', () => {
      CONFIG.collapsed = !CONFIG.collapsed;
      const content = document.getElementById('ui-content');
      const btn = document.getElementById('collapse-btn');
      content.style.display = CONFIG.collapsed ? 'none' : 'block';
      btn.textContent = CONFIG.collapsed ? '▶' : '▼';
    });
  }

  function makeDraggable(element) {
    const header = element.querySelector('#ui-header');
    let isDragging = false;
    let currentX, currentY, initialX, initialY;

    header.addEventListener('mousedown', (e) => {
      if (e.target.id === 'collapse-btn') return;
      isDragging = true;
      initialX = e.clientX - CONFIG.position.x;
      initialY = e.clientY - CONFIG.position.y;
    });

    document.addEventListener('mousemove', (e) => {
      if (!isDragging) return;
      e.preventDefault();

      currentX = e.clientX - initialX;
      currentY = e.clientY - initialY;

      CONFIG.position.x = Math.max(0, Math.min(window.innerWidth - element.offsetWidth, currentX));
      CONFIG.position.y = Math.max(0, Math.min(window.innerHeight - element.offsetHeight, currentY));

      element.style.right = `${CONFIG.position.x}px`;
      element.style.top = `${CONFIG.position.y}px`;
    });

    document.addEventListener('mouseup', () => {
      isDragging = false;
    });
  }

  function updateMetrics(calc, targets) {
    if (!calc) return;

    const currentRatioEl = document.getElementById('current-ratio');
    const optimalRatioEl = document.getElementById('optimal-ratio');
    const sliderValueEl = document.getElementById('slider-value');
    const statusTextEl = document.getElementById('status-text');
    const riskLevelEl = document.getElementById('risk-level');
    const targetListEl = document.getElementById('target-list');

    if (currentRatioEl) {
      currentRatioEl.textContent = `${(calc.currentRatio * 100).toFixed(1)}%`;

      // Color based on how close to optimal
      const diff = Math.abs(calc.currentRatio - calc.optimalRatio);
      if (diff < CONFIG.tolerance) {
        currentRatioEl.style.color = '#10b981'; // Green
      } else if (diff < CONFIG.tolerance * 2) {
        currentRatioEl.style.color = '#f59e0b'; // Orange
      } else {
        currentRatioEl.style.color = '#ef4444'; // Red
      }
    }

    // Update optimal ratio display (dynamic based on maxTroops)
    if (optimalRatioEl) {
      optimalRatioEl.textContent = `${(calc.optimalRatio * 100).toFixed(1)}%`;
    }

    if (sliderValueEl) {
      const minRatio = CONFIG.minAttackRatio * 100;
      sliderValueEl.textContent = `${calc.targetSlider}% ${calc.targetSlider > 0 && calc.targetSlider < minRatio + 5 ? '(min)' : ''}`;
    }

    if (riskLevelEl) {
      riskLevelEl.textContent = calc.riskLevel;

      // Color based on risk
      if (calc.riskLevel === 'LOW') {
        riskLevelEl.style.color = '#10b981';
      } else if (calc.riskLevel === 'MEDIUM') {
        riskLevelEl.style.color = '#f59e0b';
      } else if (calc.riskLevel === 'HIGH') {
        riskLevelEl.style.color = '#f97316';
      } else {
        riskLevelEl.style.color = '#ef4444';
      }
    }

    if (statusTextEl) {
      statusTextEl.textContent = calc.status;

      // Color based on status
      if (calc.status === 'OPTIMAL') {
        statusTextEl.style.color = '#10b981';
      } else if (calc.status === 'GROWING') {
        statusTextEl.style.color = '#3b82f6';
      } else if (calc.status === 'DEFENSIVE') {
        statusTextEl.style.color = '#ef4444';
      } else {
        statusTextEl.style.color = '#f59e0b';
      }
    }

    // Update targets list (sorted easiest to hardest)
    if (targetListEl) {
      if (targets && Array.isArray(targets) && targets.length > 0) {
        let html = '';

        targets.forEach((target, index) => {
          const winColor = target.winChance > 0.7 ? '#10b981' : target.winChance > 0.4 ? '#f59e0b' : '#ef4444';
          const winIcon = target.winChance > 0.7 ? '✅' : target.winChance > 0.4 ? '⚠️' : '❌';
          const bgColor = index === 0 ? 'rgba(16,185,129,0.15)' : 'rgba(0,0,0,0.2)';

          html += `
            <div style="
              padding: 6px 8px;
              margin-bottom: 4px;
              background: ${bgColor};
              border-radius: 4px;
              border-left: 3px solid ${winColor};
            ">
              <div style="display: flex; justify-content: space-between; margin-bottom: 2px;">
                <strong style="color: #f1f5f9; font-size: 11px;">${index + 1}. ${target.targetName}</strong>
                ${target.isNeutral ? '<span style="color: #10b981; font-size: 9px;">NEUTRAL</span>' : ''}
              </div>
              <div style="display: flex; justify-content: space-between; font-size: 9px;">
                <span style="color: #94a3b8;">Win:</span>
                <span style="color: ${winColor}; font-weight: 700;">${winIcon} ${(target.winChance * 100).toFixed(0)}%</span>
              </div>
              ${!target.isNeutral ? `
              <div style="display: flex; justify-content: space-between; font-size: 9px;">
                <span style="color: #94a3b8;">Density:</span>
                <span style="color: #64748b;">${target.density.toFixed(1)} t/tile</span>
              </div>
              ` : ''}
            </div>
          `;
        });

        targetListEl.innerHTML = html;
      } else {
        targetListEl.innerHTML = '<span style="color: #64748b;">Scanning for neighbors...</span>';
      }
    } else {
      console.warn("[UI] Target list element not found!");
    }
  }

  function addLog(message) {
    const logContent = document.getElementById('log-content');
    if (!logContent) return;

    const timestamp = new Date().toLocaleTimeString();
    const logEntry = document.createElement('div');
    logEntry.textContent = `[${timestamp}] ${message}`;
    logEntry.style.marginBottom = '2px';

    logContent.appendChild(logEntry);

    // Keep only last 10 entries
    while (logContent.children.length > 10) {
      logContent.removeChild(logContent.firstChild);
    }

    // Auto-scroll to bottom
    logContent.parentElement.scrollTop = logContent.parentElement.scrollHeight;
  }

  /* ===== INITIALIZATION ===== */
  function init() {
    console.log("[Init] Waiting for game to load...");

    // Wait for control panel
    const checkInterval = setInterval(() => {
      const controlPanel = document.querySelector('control-panel');
      if (controlPanel) {
        console.log("[Init] ✅ Control panel found!");
        clearInterval(checkInterval);
        createUI();

        // Start main loop
        setInterval(mainLoop, 1000);
        console.log("[Init] ✅ Main loop started!");
        addLog("Tactical Manager initialized");
        addLog(`Min attack ratio: ${(CONFIG.minAttackRatio * 100).toFixed(0)}%`);
      }
    }, 1000);
  }

  // Start when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
  } else {
    init();
  }

})();
