# Smart Slider Manager v7.0 - User Guide

## 🎯 **What This Script Does**

v7.0 is a **focused, reliable slider management tool** that:

✅ **Automatically adjusts your attack slider** for optimal population growth  
✅ **Tests slider manipulation** on startup to ensure it works  
✅ **Shows real-time metrics** comparing current vs optimal ratio  
✅ **Provides manual control** when you want to override automation  
✅ **Has a beautiful, draggable UI** with live status updates  
✅ **Logs all actions** so you can see what it's doing  

## 📊 **The Math Behind It**

The script uses the **mathematically optimal growth ratio of 42.2%**:

### **Growth Formula**
```
Population Growth = (10 + pop^0.73/4) × (1 - pop/maxPop)
```

### **Optimal Ratio**
At **42.2% of max population**, you get the **fastest absolute growth rate**.

### **How It Works**

1. **Below 37.2%** (42.2% - 5% tolerance):
   - Status: **GROWING** 🔵
   - Slider: **0%** (don't attack, let population grow)

2. **Between 37.2% - 47.2%**:
   - Status: **OPTIMAL** 🟢
   - Slider: **0%** (maintain optimal range)

3. **Above 47.2%**:
   - Status: **EXCESS** 🟠
   - Slider: **Calculated** to bring you back to optimal
   - Formula: `(currentPop - optimalPop) / currentPop × 100`

## 🎮 **How to Use**

### **Installation**

1. Install Tampermonkey/Greasemonkey
2. Create new script
3. Copy contents of `smart_autopilot_v7.js`
4. Save and reload OpenFrontIO

### **First Launch**

1. **Start a game** and spawn
2. **Look for the widget** in the top-right corner
3. **Wait for slider test** (happens automatically after 2 seconds)
4. **Check test status**:
   - ✅ **Slider Test PASSED** - You're good to go!
   - ❌ **SLIDER TEST FAILED** - Something's wrong, check console

### **Auto Mode** (Recommended)

1. **Enable Manager** - Check the checkbox
2. **Select "🤖 Auto (Optimal Growth)"** mode
3. **Watch the metrics**:
   - **Current Ratio** - Your current population %
   - **Optimal Ratio** - Target (42.2%)
   - **Slider** - What the script sets it to
   - **Status** - GROWING / OPTIMAL / EXCESS

The script will automatically adjust your slider every second to maintain optimal growth!

### **Manual Mode**

1. **Enable Manager** - Check the checkbox
2. **Select "🎮 Manual Control"** mode
3. **Use the slider** to set your desired attack percentage
4. The script will maintain that percentage for you

## 🎨 **UI Features**

### **Draggable**
- Click and drag the **header** to move the widget anywhere
- Position is remembered

### **Collapsible**
- Click the **▼** button to collapse/expand
- Saves screen space when you don't need to see details

### **Color-Coded Metrics**

**Current Ratio:**
- 🟢 **Green** - Within optimal range (37.2% - 47.2%)
- 🟠 **Orange** - Slightly off (47.2% - 52.2%)
- 🔴 **Red** - Far from optimal (>52.2% or <37.2%)

**Status:**
- 🟢 **OPTIMAL** - Perfect range, no action needed
- 🔵 **GROWING** - Below optimal, letting population grow
- 🟠 **EXCESS** - Above optimal, attacking to reduce

### **Live Log**
- Shows last 10 actions
- Timestamps for each entry
- Auto-scrolls to latest

## 🔍 **Console Logging**

The script logs to browser console (F12):

```
╔════════════════════════════════════════════════════════════╗
║     📊 Smart Slider Manager v7.0                         ║
╚════════════════════════════════════════════════════════════╝
[Init] Waiting for game to load...
[Init] ✅ Control panel found!
[Init] ✅ Main loop started!
[API] ✅ Game API initialized!
[API] Player: YourName
[Test] Testing slider manipulation...
[Test] ✅ Slider test PASSED!
[Auto] EXCESS: 0% → 15%
[Auto] Excess troops: 52.3% > 42.2%
[Auto] Troops: 45000/86000 (52.3%)
```

## ⚙️ **Configuration**

You can edit these values at the top of the script:

```javascript
const CONFIG = {
  enabled: false,           // Start enabled/disabled
  mode: 'auto',            // 'auto' or 'manual'
  
  optimalRatio: 0.422,     // 42.2% optimal growth
  tolerance: 0.05,         // ±5% tolerance
  
  manualSliderValue: 50,   // Default manual slider %
  
  collapsed: false,        // Start collapsed
  position: { x: 20, y: 20 } // Initial position
};
```

## 🐛 **Troubleshooting**

### **"SLIDER TEST FAILED"**

**Possible causes:**
1. Game not fully loaded yet
2. Slider element not found
3. UI state not accessible

**Solutions:**
1. Wait a few more seconds and refresh
2. Check console for errors
3. Make sure you're in an active game (not lobby)

### **Slider not moving**

**Check:**
1. Is "Enable Manager" checked?
2. Is slider test showing as PASSED?
3. Are you in auto or manual mode?
4. Check console for errors

### **Metrics showing "--"**

**Cause:** Game API not available yet

**Solution:** Wait for game to fully start and player to spawn

### **Widget disappeared**

**Cause:** Might have dragged it off-screen

**Solution:** Refresh page (position resets to default)

## 📈 **Expected Behavior**

### **Early Game (Low Population)**

```
Current Ratio: 15.3% 🔴
Optimal Ratio: 42.2%
Slider: 0%
Status: GROWING 🔵
```

The script keeps slider at 0% to let you grow to optimal.

### **Mid Game (Optimal Range)**

```
Current Ratio: 41.8% 🟢
Optimal Ratio: 42.2%
Slider: 0%
Status: OPTIMAL 🟢
```

You're in the sweet spot! No adjustments needed.

### **Late Game (Excess Troops)**

```
Current Ratio: 68.5% 🔴
Optimal Ratio: 42.2%
Slider: 38%
Status: EXCESS 🟠
```

The script sets slider to attack and bring you back to optimal.

## 🎯 **Why This Works**

1. **Optimal Growth** - 42.2% gives you the fastest absolute troop growth
2. **Automatic Adjustment** - Script constantly monitors and adjusts
3. **No Manual Work** - You can focus on strategy, not slider management
4. **Transparent** - See exactly what it's doing and why
5. **Reliable** - Tests itself on startup to ensure it works

## 🚀 **Pro Tips**

1. **Use Auto Mode** for maximum efficiency
2. **Watch the status** to understand what the script is doing
3. **Switch to Manual** when you want to make a big attack
4. **Collapse the widget** when you don't need to see it
5. **Check the log** if something seems wrong

## ✅ **Success Criteria**

The script is working correctly if:

1. ✅ Slider test shows PASSED
2. ✅ Metrics update every second
3. ✅ Slider moves when status is EXCESS
4. ✅ Current ratio trends toward 42.2%
5. ✅ Log shows recent actions

Enjoy optimal growth! 📊🚀
