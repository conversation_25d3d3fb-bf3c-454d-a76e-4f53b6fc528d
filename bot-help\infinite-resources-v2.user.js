// ==UserScript==
// @name         OpenFront.io - Infinite Resources v2
// @namespace    http://tampermonkey.net/
// @version      2.0
// @description  Enable infinite gold and troops via config interception
// <AUTHOR>
// @match        https://openfront.io/*
// @grant        none
// ==/UserScript==

(function () {
  'use strict';

  console.log('🚀 Infinite Resources v2 Loading...');

  // Method 1: Intercept the game config creation
  function interceptGameConfig() {
    // Hook into the SinglePlayerModal startGame method
    const originalCustomEvent = CustomEvent;
    window.CustomEvent = function (type, eventInitDict) {
      if (type === 'join-lobby' && eventInitDict?.detail?.gameStartInfo?.config) {
        console.log('🎯 Intercepting join-lobby event');
        const config = eventInitDict.detail.gameStartInfo.config;
        config.infiniteGold = true;
        config.infiniteTroops = true;
        config.instantBuild = true;
        console.log('✅ Modified config:', config);
      }
      return new originalCustomEvent(type, eventInitDict);
    };
  }

  // Method 2: Intercept fetch requests to modify server responses
  function interceptFetch() {
    const originalFetch = window.fetch;
    window.fetch = async function (...args) {
      const response = await originalFetch.apply(this, args);

      // Clone response to modify it
      const clonedResponse = response.clone();

      try {
        const text = await clonedResponse.text();
        if (text.includes('"gameStartInfo"') && text.includes('"config"')) {
          console.log('🎯 Intercepting game start response');
          const data = JSON.parse(text);

          if (data.gameStartInfo?.config) {
            data.gameStartInfo.config.infiniteGold = true;
            data.gameStartInfo.config.infiniteTroops = true;
            data.gameStartInfo.config.instantBuild = true;
            console.log('✅ Modified server response config');
          }

          // Return modified response
          return new Response(JSON.stringify(data), {
            status: response.status,
            statusText: response.statusText,
            headers: response.headers
          });
        }
      } catch (e) {
        // Not JSON, return original
      }

      return response;
    };
  }

  // Method 3: Hook into WebSocket messages with proper connection handling
  function interceptWebSocket() {
    const originalWebSocket = window.WebSocket;
    window.WebSocket = function (url, protocols) {
      console.log('🔌 Creating WebSocket connection to:', url);
      const ws = new originalWebSocket(url, protocols);

      // Store original handlers
      const originalOnOpen = ws.onopen;
      const originalOnMessage = ws.onmessage;
      const originalOnError = ws.onerror;
      const originalOnClose = ws.onclose;

      // Enhanced connection handling
      ws.onopen = function (event) {
        console.log('✅ WebSocket connected successfully');
        if (originalOnOpen) {
          originalOnOpen.call(this, event);
        }
      };

      ws.onerror = function (event) {
        console.log('❌ WebSocket error:', event);
        if (originalOnError) {
          originalOnError.call(this, event);
        }
      };

      ws.onclose = function (event) {
        console.log('🔌 WebSocket closed:', event.code, event.reason);
        if (originalOnClose) {
          originalOnClose.call(this, event);
        }
      };

      // Message interception with better error handling
      ws.onmessage = function (event) {
        try {
          const data = JSON.parse(event.data);

          // Log all message types for debugging
          console.log('📨 WebSocket message type:', data.type);

          if (data.type === 'start' && data.gameStartInfo?.config) {
            console.log('🎯 Intercepting WebSocket start message');
            console.log('Original config:', data.gameStartInfo.config);

            // Try to modify config (works for host-created games)
            data.gameStartInfo.config.infiniteGold = true;
            data.gameStartInfo.config.infiniteTroops = true;
            data.gameStartInfo.config.instantBuild = true;

            // Also modify player resources directly (works for joined games)
            if (window.modifyPlayerResources) {
              data = window.modifyPlayerResources(data);
            }

            console.log('✅ Modified config:', data.gameStartInfo.config);

            // Create modified event
            const modifiedEvent = new MessageEvent('message', {
              data: JSON.stringify(data),
              origin: event.origin,
              source: event.source
            });

            if (originalOnMessage) {
              originalOnMessage.call(this, modifiedEvent);
            }
            return;
          }

          // Intercept game state updates
          if (data.type === 'update' || data.type === 'turn' || data.players) {
            if (window.modifyPlayerResources) {
              data = window.modifyPlayerResources(data);
            }
          }
        } catch (e) {
          console.log('📨 Non-JSON WebSocket message or parsing error:', e);
        }

        // Pass through original message
        if (originalOnMessage) {
          originalOnMessage.call(this, event);
        }
      };

      return ws;
    };
  }

  // Method 4: Intercept console.log to catch and handle WebSocket errors
  function interceptWebSocketErrors() {
    const originalConsoleLog = console.log;
    console.log = function (...args) {
      const message = args.join(' ');

      // Detect the specific WebSocket error
      if (message.includes('WebSocket is not open. Current state:') && message.includes('attempting reconnect')) {
        console.log('🔧 Detected WebSocket connection issue, applying fix...');

        // Try to find and fix the Transport instance
        setTimeout(() => {
          // Look for Transport instances in the global scope or common locations
          const possibleTransports = [];

          // Check for common variable names that might hold the transport
          if (window.gameTransport) possibleTransports.push(window.gameTransport);
          if (window.transport) possibleTransports.push(window.transport);

          // Check for instances in common game objects
          if (window.game && window.game.transport) possibleTransports.push(window.game.transport);

          possibleTransports.forEach(transport => {
            if (transport && transport.socket && transport.reconnect) {
              console.log('🔧 Found transport instance, forcing reconnection...');
              transport.reconnect();
            }
          });
        }, 100);
      }

      // Call original console.log
      originalConsoleLog.apply(this, args);
    };
  }

  // Method 5: Enhanced join-lobby event handling for multiplayer
  function interceptJoinLobby() {
    document.addEventListener('join-lobby', function (event) {
      console.log('🎯 Intercepting join-lobby event for multiplayer');
      console.log('Event detail:', event.detail);

      // Check if this is a multiplayer game (has gameRecord instead of gameStartInfo)
      if (event.detail?.gameRecord) {
        console.log('🌐 Detected multiplayer game join');

        // Store the game config for later modification when the start message arrives
        window.multiplayerGameConfig = {
          infiniteGold: true,
          infiniteTroops: true,
          instantBuild: true
        };

        console.log('✅ Stored multiplayer config for later application');
      }
    }, true);
  }

  // Method 6: Direct WebSocket readyState monitoring and fixing
  function monitorWebSocketState() {
    // Override WebSocket.prototype.send to handle timing issues
    const originalSend = WebSocket.prototype.send;
    WebSocket.prototype.send = function (data) {
      if (this.readyState === WebSocket.CONNECTING) {
        console.log('⏳ WebSocket still connecting, waiting...');

        // Wait for connection to be ready
        const waitAndSend = () => {
          if (this.readyState === WebSocket.OPEN) {
            console.log('✅ WebSocket ready, sending delayed message');
            originalSend.call(this, data);
          } else if (this.readyState === WebSocket.CONNECTING) {
            setTimeout(waitAndSend, 50);
          } else {
            console.log('❌ WebSocket connection failed, message dropped');
          }
        };

        setTimeout(waitAndSend, 50);
      } else if (this.readyState === WebSocket.OPEN) {
        originalSend.call(this, data);
      } else {
        console.log('❌ WebSocket not ready, state:', this.readyState);
      }
    };
  }

  // Method 7: Client-side resource manipulation for joined games
  function manipulateClientResources() {
    // Wait for game to be available
    const checkForGame = () => {
      // Look for common game object locations
      const gameLocations = [
        window.game,
        window.gameInstance,
        window.gameState,
        window.currentGame
      ];

      for (const game of gameLocations) {
        if (game && game.players) {
          console.log('🎮 Found game instance, setting up resource manipulation');
          setupResourceHooks(game);
          return;
        }
      }

      // Check for game in common frameworks
      if (window.React && window.React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED) {
        // Try to find React component with game state
        console.log('🔍 Searching React components for game state...');
      }

      // Keep checking
      setTimeout(checkForGame, 1000);
    };

    const setupResourceHooks = (game) => {
      console.log('🔧 Setting up resource manipulation hooks');

      // Hook into player resource updates
      if (game.players) {
        game.players.forEach(player => {
          if (player.resources) {
            // Override resource getters/setters
            const originalGold = player.resources.gold || 0;
            const originalTroops = player.resources.troops || 0;

            Object.defineProperty(player.resources, 'gold', {
              get: () => 999999,
              set: (value) => { /* ignore */ },
              enumerable: true,
              configurable: true
            });

            Object.defineProperty(player.resources, 'troops', {
              get: () => 999999,
              set: (value) => { /* ignore */ },
              enumerable: true,
              configurable: true
            });

            console.log('✅ Resource hooks installed for player:', player.username);
          }
        });
      }
    };

    // Start checking for game
    setTimeout(checkForGame, 2000);
  }

  // Method 8: Intercept game update messages to modify your player's resources
  function interceptGameUpdates() {
    // This will be called from the WebSocket message handler
    window.modifyPlayerResources = (gameData) => {
      if (gameData && gameData.players) {
        // Find your player (you'll need to identify which one is yours)
        const yourClientID = window.clientID || localStorage.getItem('clientID');

        gameData.players.forEach(player => {
          if (player.clientID === yourClientID) {
            console.log('💰 Modifying your resources');
            if (player.resources) {
              player.resources.gold = 999999;
              player.resources.troops = 999999;
            }
            if (player.gold !== undefined) player.gold = 999999;
            if (player.troops !== undefined) player.troops = 999999;
          }
        });
      }
      return gameData;
    };
  }

  // Apply all interception methods
  interceptGameConfig();
  interceptFetch();
  interceptWebSocket();
  interceptWebSocketErrors();
  interceptJoinLobby();
  monitorWebSocketState();
  manipulateClientResources();
  interceptGameUpdates();

  console.log('✅ All interception hooks installed');

})();
