{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug Jest Tests", "runtimeExecutable": "npm", "runtimeArgs": ["run-script", "test"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}, {"type": "node", "request": "launch", "name": "Debug Server", "runtimeExecutable": "node", "runtimeArgs": ["--loader", "ts-node/esm", "--experimental-specifier-resolution=node", "${workspaceFolder}/src/server/Server.ts"], "env": {"GAME_ENV": "dev"}, "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true, "restart": true}]}