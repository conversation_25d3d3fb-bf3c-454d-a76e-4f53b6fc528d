# 📖 New Player Bot Assistant - Usage Guide

## 🚀 Quick Start

### 1. Installation
1. Install [Tampermonkey](https://www.tampermonkey.net/) browser extension
2. Copy the contents of `new-player-bot-assistant.user.js`
3. Create a new script in Tam<PERSON><PERSON><PERSON> and paste the code
4. Save the script (Ctrl+S)
5. Go to [OpenFrontIO](https://openfront.io/) and start a game

### 2. First Launch
When you join a game, the bot widget will appear in the top-left corner with:
- 🤖 **Bot Controls** - Enable/disable autopilot and select mode
- 🧪 **Slider Test** - Test if the bot can control your attack slider
- 📊 **Status Display** - Current game state and metrics
- 💡 **Recommendations** - What the bot suggests you do
- 🎯 **Target Analysis** - Best neighbors to attack
- 📈 **Performance Stats** - Track your progress

## 🎮 Using the Bot

### Bot Modes Explained

#### 🔥 Aggressive Mode
- **Best for**: Experienced players, favorable map positions
- **Strategy**: High-risk, high-reward gameplay
- **Attack Ratios**: 15-60% of troops
- **Behavior**: 
  - Attacks more frequently
  - Takes bigger risks for territory
  - Maintains lower reserves
  - Prioritizes expansion over safety

#### ⚖️ Balanced Mode (Recommended for New Players)
- **Best for**: Learning the game, general gameplay
- **Strategy**: Optimal growth with moderate risk
- **Attack Ratios**: 10-50% of troops
- **Behavior**:
  - Follows the 42% optimal ratio formula
  - Balances offense and defense
  - Adapts to threats automatically
  - Good survival rate

#### 🛡️ Defensive Mode
- **Best for**: Difficult positions, surrounded by enemies
- **Strategy**: Conservative play, survival focus
- **Attack Ratios**: 5-40% of troops
- **Behavior**:
  - Maintains high reserves
  - Only attacks very weak targets
  - Prioritizes defense over expansion
  - Best for learning without pressure

#### 🎯 Manual Mode
- **Best for**: Learning bot strategy, specific tactics
- **Strategy**: You control the slider, bot provides advice
- **Behavior**:
  - Bot never moves your slider
  - Shows recommendations and analysis
  - Perfect for understanding the math
  - Learn when and why to attack

### Understanding the Status Display

#### Status Indicators
- **🚨 CRITICAL** - Very low troops, defend only
- **⚠️ DEFENSIVE** - Under attack, limited offense
- **🔵 GROWING** - Building troops to optimal level
- **🟢 OPTIMAL** - Perfect range, maintain current strategy
- **🟡 EXCESS** - Too many troops, should attack

#### Key Metrics
- **Current Ratio**: Your troops ÷ max troops (as percentage)
- **Optimal Ratio**: Target percentage for fastest growth
- **Attack Slider**: Bot's recommended attack percentage
- **Risk Level**: Current threat assessment
- **Threats**: Number of dangerous neighbors

### Target Analysis Explained

The bot shows up to 5 best targets, sorted by priority:

#### Target Information
- **Win Chance**: Estimated probability of successful attack
  - 🟢 70%+ = Easy target
  - 🟡 40-70% = Moderate difficulty  
  - 🔴 <40% = High risk
- **Recommended Ratio**: Percentage of troops to send
- **Threat Level**: How dangerous this neighbor is to you
- **Momentum**: Whether they're growing (↗), stable (→), or shrinking (↘)

#### Attack Priority
1. **Terra Nullius** (Neutral land) - Always prioritized
2. **Weak enemies** - High win chance, low threat
3. **Moderate enemies** - Balanced risk/reward
4. **Strong enemies** - Only when you have excess troops

## 🧪 Slider Testing

### Why Test the Slider?
The slider test verifies that the bot can control your attack slider. This is crucial because:
- Some browser settings block automated controls
- Game updates might change the slider implementation
- Network lag can interfere with slider updates

### How to Test
1. Click the "Test Slider Control" button
2. Watch your attack slider move: 0% → 25% → 50% → 25% → 0%
3. Status shows:
   - **Testing...** - Test in progress
   - **Test Complete ✅** - Slider control works
   - **Test Failed ❌** - Bot cannot control slider

### If Test Fails
1. Refresh the page and try again
2. Check if Tampermonkey is enabled
3. Disable other userscripts that might interfere
4. Try a different browser
5. Check browser console (F12) for error messages

## 📊 Performance Tracking

### Metrics Explained
- **Session Time**: How long you've been playing
- **Troop Growth**: Net change in troops since start
- **Territory Change**: Net territories gained/lost
- **Peak Troops**: Highest troop count achieved

### Using Performance Data
- **Positive growth** = Bot is working well
- **Negative growth** = Adjust strategy or mode
- **Territory gains** = Successful expansion
- **High peak troops** = Good growth management

## ⚙️ Advanced Configuration

### Widget Customization
- **Drag to Move**: Click and drag the header to reposition
- **Collapse/Expand**: Click ▼/▲ to hide/show details
- **Position Saved**: Widget remembers its position

### Mode Switching
You can change modes anytime:
1. **During Combat**: Switch to Defensive if under heavy attack
2. **Early Game**: Use Balanced for steady growth
3. **Late Game**: Switch to Aggressive for final push
4. **Learning**: Use Manual to understand strategy

## 🎯 Strategy Tips

### Early Game (First 5 minutes)
- Bot keeps slider at 0% to build troops
- Focus on claiming Terra Nullius
- Avoid attacking strong players
- Let population grow to ~40% of max

### Mid Game (5-15 minutes)
- Bot starts attacking when you have excess troops
- Targets weakest neighbors first
- Maintains reserves for defense
- Adapts to threats automatically

### Late Game (15+ minutes)
- More aggressive expansion
- Pushes for territory control
- Manages multiple threats
- Optimizes for final ranking

### Under Attack
- Bot automatically reserves 30% troops for defense
- Reduces attack slider significantly
- Prioritizes survival over expansion
- Counter-attacks when safe

## 🐛 Troubleshooting

### Bot Not Working?
1. **Check Enable Checkbox**: Must be checked for autopilot
2. **Verify Mode**: Manual mode won't move slider
3. **Game State**: Must be in active game, not lobby
4. **Slider Test**: Run test to verify control works

### Slider Not Moving?
1. **Test First**: Use the slider test button
2. **Browser Issues**: Try refreshing the page
3. **Other Scripts**: Disable conflicting userscripts
4. **Game Updates**: Check if game changed slider implementation

### Widget Issues?
1. **Disappeared**: Refresh page (position resets)
2. **Collapsed**: Look for small collapsed widget
3. **Position**: Drag header to move widget
4. **Performance**: Disable performance tracking if slow

### Performance Problems?
1. **Close Other Tabs**: Reduce browser load
2. **Disable Tracking**: Turn off performance stats
3. **Modern Browser**: Use Chrome, Firefox, or Edge
4. **Clear Cache**: Clear browser cache if issues persist

## 🤝 Getting Help

### Console Logging
The bot logs detailed information to browser console (F12):
- `[Bot]` - Bot actions and decisions
- `[API]` - Game API status
- `[Test]` - Slider test results
- `[Init]` - Startup information

### Common Log Messages
- `✅ Game API initialized!` - Bot connected to game
- `🤖 Autopilot enabled` - Bot is active
- `⏸️ Autopilot paused` - Bot is disabled
- `🧪 Starting slider control test` - Testing slider
- `❌ Slider test failed` - Control not working

### Debug Information
Access debug info in console:
```javascript
// Check bot status
window.newPlayerBot

// View current config
console.log(CONFIG)

// View current state
console.log(STATE)
```

## 📈 Expected Results

### With Balanced Mode
- **Early Game**: Steady growth, minimal losses
- **Mid Game**: Consistent territory expansion  
- **Late Game**: Competitive with experienced players
- **Survival Rate**: 60-80% higher than manual play

### Performance Benchmarks
- **New Players**: 2-3x better survival rate
- **Territory Growth**: 50-100% more territories
- **Troop Efficiency**: Optimal growth curve maintenance
- **Threat Response**: Automatic defensive adjustments

Remember: The bot is a learning tool. Watch its decisions and learn the strategy for when you want to play manually!
